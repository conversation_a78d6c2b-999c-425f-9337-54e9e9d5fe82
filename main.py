#!/usr/bin/env python3
"""
AI-Enhanced Video Player with Real-time Upscaling
Supports ALL available AI models with ROCm acceleration for AMD GPUs
"""

import sys
import os
import logging
import asyncio
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QIcon

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from ui.vlc_style_window import VLCStyleMainWindow
from utils.logger import setup_logging
from models.model_manager import model_manager

class AIVideoPlayerApp:
    """Main application class"""

    def __init__(self):
        self.app = None
        self.main_window = None
        self.setup_logging()

    def setup_logging(self):
        """Setup application logging"""
        setup_logging(
            log_level=logging.INFO,
            log_file=config.logs_dir / "app.log",
            console_output=True
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info("AI Video Player starting...")

    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available"""
        try:
            import torch
            import cv2
            import numpy as np
            from PyQt6 import QtWidgets

            # Check ROCm availability
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
                self.logger.info(f"ROCm/CUDA available: {device_count} device(s)")
                self.logger.info(f"Primary device: {device_name}")
            else:
                self.logger.warning("ROCm/CUDA not available, will use CPU")

            return True

        except ImportError as e:
            self.logger.error(f"Missing dependency: {e}")
            return False

    def setup_application(self):
        """Setup Qt application"""
        # Enable high DPI scaling
        QApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
        )

        self.app = QApplication(sys.argv)
        self.app.setApplicationName("AI Video Player")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("AI Video Player")

        # Set application icon if available
        icon_path = project_root / "assets" / "icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))

        # Apply theme
        self.apply_theme()

        self.logger.info("Qt application initialized")

    def apply_theme(self):
        """Apply application theme"""
        try:
            if config.ui.theme == "dark":
                import qdarkstyle
                self.app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
                self.logger.info("Applied dark theme")
            elif config.ui.theme == "light":
                # Use default light theme
                self.app.setStyleSheet("")
                self.logger.info("Applied light theme")
            # Auto theme detection could be added here

        except ImportError:
            self.logger.warning("qdarkstyle not available, using default theme")

    def create_main_window(self):
        """Create and setup main window"""
        try:
            self.main_window = VLCStyleMainWindow()

            # Apply window settings from config
            if config.ui.window_size:
                self.main_window.resize(config.ui.window_size[0], config.ui.window_size[1])

            if config.ui.fullscreen:
                self.main_window.showFullScreen()
            else:
                self.main_window.show()

            self.logger.info("Main window created and displayed")

        except Exception as e:
            self.logger.error(f"Error creating main window: {e}")
            raise

    def setup_model_manager(self):
        """Initialize model manager"""
        try:
            # Model manager is already initialized as global instance
            available_models = model_manager.get_available_models()
            all_models = model_manager.get_all_models()

            self.logger.info(f"Model manager initialized")
            self.logger.info(f"Available models: {len(available_models)}/{len(all_models)}")
            self.logger.info(f"Available: {available_models}")

            if not available_models:
                self.logger.warning("No model weights found. Please download model weights to the models/ directory")

        except Exception as e:
            self.logger.error(f"Error initializing model manager: {e}")
            raise

    def show_startup_info(self):
        """Show startup information dialog"""
        if not model_manager.get_available_models():
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setWindowTitle("Model Weights Required")
            msg.setText("No AI model weights found.")
            msg.setInformativeText(
                "To use AI upscaling features, please download model weights and place them in the 'models/' directory.\n\n"
                "Supported models include:\n"
                "• Real-ESRGAN (RealESRGAN_x4plus.pth)\n"
                "• ESRGAN (esrgan_x4.pth)\n"
                "• Waifu2x (waifu2x_art.pth)\n"
                "• EDSR (edsr_x4.pth)\n"
                "• And many more...\n\n"
                "The application will work without AI upscaling until models are available."
            )
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg.exec()

    def run(self):
        """Run the application"""
        try:
            # Check dependencies
            if not self.check_dependencies():
                print("Error: Missing required dependencies")
                return 1

            # Setup Qt application
            self.setup_application()

            # Initialize model manager
            self.setup_model_manager()

            # Create main window
            self.create_main_window()

            # Show startup info if needed
            QTimer.singleShot(1000, self.show_startup_info)  # Delay to show after window

            # Start event loop
            self.logger.info("Starting application event loop")
            return self.app.exec()

        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
            return 0
        except Exception as e:
            self.logger.error(f"Application error: {e}", exc_info=True)

            # Show error dialog if Qt app is available
            if self.app:
                msg = QMessageBox()
                msg.setIcon(QMessageBox.Icon.Critical)
                msg.setWindowTitle("Application Error")
                msg.setText("An error occurred while starting the application.")
                msg.setDetailedText(str(e))
                msg.exec()

            return 1
        finally:
            self.cleanup()

    def cleanup(self):
        """Cleanup resources"""
        try:
            # Unload all models
            if 'model_manager' in globals():
                model_manager.unload_all_models()
                self.logger.info("Unloaded all AI models")

            # Save configuration
            config.save_config()
            self.logger.info("Configuration saved")

            self.logger.info("Application cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

def main():
    """Main entry point"""
    app = AIVideoPlayerApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
