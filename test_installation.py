#!/usr/bin/env python3
"""
Installation Test Script for AI Video Player
Verifies all dependencies and functionality
"""

import sys
import importlib
import subprocess
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_python_version():
    """Test Python version"""
    logger.info("Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        logger.info(f"✓ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        logger.error(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_import(module_name, package_name=None):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        display_name = package_name or module_name
        logger.info(f"✓ {display_name} - OK")
        return True
    except ImportError as e:
        display_name = package_name or module_name
        logger.error(f"✗ {display_name} - MISSING ({e})")
        return False

def test_pytorch_rocm():
    """Test PyTorch with ROCm support"""
    logger.info("Testing PyTorch with ROCm...")
    try:
        import torch
        logger.info(f"✓ PyTorch {torch.__version__} - OK")
        
        # Test CUDA/ROCm availability
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            logger.info(f"✓ ROCm/CUDA available - {device_count} device(s)")
            logger.info(f"  Primary device: {device_name}")
            return True
        else:
            logger.warning("⚠ ROCm/CUDA not available - will use CPU")
            return True  # Still functional, just slower
    except ImportError:
        logger.error("✗ PyTorch - MISSING")
        return False

def test_qt():
    """Test Qt6 availability"""
    logger.info("Testing Qt6...")
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        from PyQt6.QtGui import QIcon
        logger.info("✓ PyQt6 - OK")
        return True
    except ImportError:
        logger.error("✗ PyQt6 - MISSING")
        return False

def test_opencv():
    """Test OpenCV"""
    logger.info("Testing OpenCV...")
    try:
        import cv2
        logger.info(f"✓ OpenCV {cv2.__version__} - OK")
        return True
    except ImportError:
        logger.error("✗ OpenCV - MISSING")
        return False

def test_project_structure():
    """Test project file structure"""
    logger.info("Testing project structure...")
    
    required_files = [
        "main.py",
        "config.py",
        "requirements.txt",
        "models/__init__.py",
        "models/model_manager.py",
        "ui/__init__.py",
        "ui/main_window.py",
        "utils/__init__.py",
        "utils/logger.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"✗ Missing files: {missing_files}")
        return False
    else:
        logger.info("✓ Project structure - OK")
        return True

def test_model_manager():
    """Test model manager functionality"""
    logger.info("Testing model manager...")
    try:
        from models.model_manager import model_manager
        from config import config
        
        # Test basic functionality
        all_models = model_manager.get_all_models()
        available_models = model_manager.get_available_models()
        
        logger.info(f"✓ Model manager - OK")
        logger.info(f"  Total models supported: {len(all_models)}")
        logger.info(f"  Models with weights: {len(available_models)}")
        
        if available_models:
            logger.info(f"  Available: {', '.join(available_models[:5])}{'...' if len(available_models) > 5 else ''}")
        else:
            logger.warning("  ⚠ No model weights found - download models to test AI features")
        
        return True
    except Exception as e:
        logger.error(f"✗ Model manager - ERROR ({e})")
        return False

def test_gui_creation():
    """Test GUI creation without showing"""
    logger.info("Testing GUI creation...")
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Try to create main window
        window = MainWindow()
        logger.info("✓ GUI creation - OK")
        
        # Cleanup
        window.close()
        return True
        
    except Exception as e:
        logger.error(f"✗ GUI creation - ERROR ({e})")
        return False

def test_rocm_tools():
    """Test ROCm tools availability"""
    logger.info("Testing ROCm tools...")
    try:
        result = subprocess.run(['rocm-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info("✓ rocm-smi - OK")
            # Parse GPU info
            lines = result.stdout.split('\n')
            gpu_lines = [line for line in lines if 'GPU' in line and 'Temp' in line]
            if gpu_lines:
                logger.info(f"  Detected {len(gpu_lines)} GPU(s)")
            return True
        else:
            logger.warning("⚠ rocm-smi - Not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        logger.warning("⚠ rocm-smi - Not found (ROCm may not be installed)")
        return False

def run_all_tests():
    """Run all tests and return summary"""
    logger.info("=" * 50)
    logger.info("AI Video Player Installation Test")
    logger.info("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Project Structure", test_project_structure),
        ("PyTorch + ROCm", test_pytorch_rocm),
        ("Qt6", test_qt),
        ("OpenCV", test_opencv),
        ("NumPy", lambda: test_import("numpy", "NumPy")),
        ("Pillow", lambda: test_import("PIL", "Pillow")),
        ("Model Manager", test_model_manager),
        ("GUI Creation", test_gui_creation),
        ("ROCm Tools", test_rocm_tools),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"✗ {test_name} - EXCEPTION ({e})")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        logger.info(f"{symbol} {test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The AI Video Player should work correctly.")
        return True
    else:
        logger.warning("⚠ Some tests failed. Check the errors above.")
        
        # Provide helpful suggestions
        logger.info("\nSuggestions:")
        for test_name, result in results:
            if not result:
                if "PyTorch" in test_name:
                    logger.info("- Install PyTorch with ROCm: pip install torch torchvision --index-url https://download.pytorch.org/whl/rocm5.7")
                elif "Qt6" in test_name:
                    logger.info("- Install PyQt6: pip install PyQt6")
                elif "OpenCV" in test_name:
                    logger.info("- Install OpenCV: pip install opencv-python")
                elif "Model Manager" in test_name:
                    logger.info("- Check config.py and model imports")
                elif "ROCm" in test_name:
                    logger.info("- Install ROCm drivers and tools")
        
        return False

def main():
    """Main function"""
    success = run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
