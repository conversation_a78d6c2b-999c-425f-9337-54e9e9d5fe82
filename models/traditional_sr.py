"""
Traditional Super Resolution Models
SRCNN, VDSR, EDSR, MDSR implementations with ROCm support
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import logging
from typing import Optional

from .model_manager import BaseModel
from config import ModelConfig

logger = logging.getLogger(__name__)

class SRCNNNet(nn.Module):
    """Super-Resolution Convolutional Neural Network"""
    
    def __init__(self, scale_factor=2, num_channels=3):
        super(SRCNNNet, self).__init__()
        self.scale_factor = scale_factor
        
        # Feature extraction
        self.conv1 = nn.Conv2d(num_channels, 64, kernel_size=9, padding=4)
        
        # Non-linear mapping
        self.conv2 = nn.Conv2d(64, 32, kernel_size=1, padding=0)
        
        # Reconstruction
        self.conv3 = nn.Conv2d(32, num_channels, kernel_size=5, padding=2)
        
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        # Bicubic upsampling first
        x = F.interpolate(x, scale_factor=self.scale_factor, mode='bicubic', align_corners=False)
        
        # SRCNN processing
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.conv3(x)
        
        return x

class VDSRNet(nn.Module):
    """Very Deep Super Resolution Network"""
    
    def __init__(self, scale_factor=2, num_channels=3, num_layers=20):
        super(VDSRNet, self).__init__()
        self.scale_factor = scale_factor
        
        # Initial convolution
        self.input_conv = nn.Conv2d(num_channels, 64, kernel_size=3, padding=1)
        
        # Residual layers
        self.residual_layers = nn.ModuleList([
            nn.Conv2d(64, 64, kernel_size=3, padding=1) for _ in range(num_layers)
        ])
        
        # Output convolution
        self.output_conv = nn.Conv2d(64, num_channels, kernel_size=3, padding=1)
        
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        # Bicubic upsampling
        x = F.interpolate(x, scale_factor=self.scale_factor, mode='bicubic', align_corners=False)
        residual = x
        
        # Initial convolution
        x = self.relu(self.input_conv(x))
        
        # Residual layers
        for layer in self.residual_layers:
            x = self.relu(layer(x))
        
        # Output
        x = self.output_conv(x)
        
        # Global residual connection
        return x + residual

class ResidualBlock(nn.Module):
    """Residual block for EDSR"""
    
    def __init__(self, num_features, res_scale=1.0):
        super(ResidualBlock, self).__init__()
        self.res_scale = res_scale
        
        self.conv1 = nn.Conv2d(num_features, num_features, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(num_features, num_features, kernel_size=3, padding=1)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        residual = x
        x = self.relu(self.conv1(x))
        x = self.conv2(x)
        return residual + x * self.res_scale

class EDSRNet(nn.Module):
    """Enhanced Deep Super Resolution Network"""
    
    def __init__(self, scale_factor=4, num_channels=3, num_features=256, num_blocks=32):
        super(EDSRNet, self).__init__()
        self.scale_factor = scale_factor
        
        # Head
        self.head = nn.Conv2d(num_channels, num_features, kernel_size=3, padding=1)
        
        # Body
        self.body = nn.ModuleList([
            ResidualBlock(num_features) for _ in range(num_blocks)
        ])
        self.body_conv = nn.Conv2d(num_features, num_features, kernel_size=3, padding=1)
        
        # Tail
        if scale_factor == 2:
            self.tail = nn.Sequential(
                nn.Conv2d(num_features, num_features * 4, kernel_size=3, padding=1),
                nn.PixelShuffle(2),
                nn.Conv2d(num_features, num_channels, kernel_size=3, padding=1)
            )
        elif scale_factor == 4:
            self.tail = nn.Sequential(
                nn.Conv2d(num_features, num_features * 4, kernel_size=3, padding=1),
                nn.PixelShuffle(2),
                nn.Conv2d(num_features, num_features * 4, kernel_size=3, padding=1),
                nn.PixelShuffle(2),
                nn.Conv2d(num_features, num_channels, kernel_size=3, padding=1)
            )
        else:
            # Fallback for other scales
            self.tail = nn.Sequential(
                nn.Conv2d(num_features, num_channels * (scale_factor ** 2), kernel_size=3, padding=1),
                nn.PixelShuffle(scale_factor)
            )
    
    def forward(self, x):
        # Head
        x = self.head(x)
        residual = x
        
        # Body
        for block in self.body:
            x = block(x)
        x = self.body_conv(x)
        x = x + residual
        
        # Tail
        x = self.tail(x)
        
        return x

class MDSRNet(nn.Module):
    """Multi-scale Deep Super Resolution Network"""
    
    def __init__(self, scale_factors=[2, 3, 4], num_channels=3, num_features=64, num_blocks=16):
        super(MDSRNet, self).__init__()
        self.scale_factors = scale_factors
        
        # Shared feature extraction
        self.head = nn.Conv2d(num_channels, num_features, kernel_size=3, padding=1)
        
        # Shared body
        self.body = nn.ModuleList([
            ResidualBlock(num_features) for _ in range(num_blocks)
        ])
        self.body_conv = nn.Conv2d(num_features, num_features, kernel_size=3, padding=1)
        
        # Scale-specific tails
        self.tails = nn.ModuleDict()
        for scale in scale_factors:
            if scale == 2:
                tail = nn.Sequential(
                    nn.Conv2d(num_features, num_features * 4, kernel_size=3, padding=1),
                    nn.PixelShuffle(2),
                    nn.Conv2d(num_features, num_channels, kernel_size=3, padding=1)
                )
            elif scale == 3:
                tail = nn.Sequential(
                    nn.Conv2d(num_features, num_features * 9, kernel_size=3, padding=1),
                    nn.PixelShuffle(3),
                    nn.Conv2d(num_features, num_channels, kernel_size=3, padding=1)
                )
            elif scale == 4:
                tail = nn.Sequential(
                    nn.Conv2d(num_features, num_features * 4, kernel_size=3, padding=1),
                    nn.PixelShuffle(2),
                    nn.Conv2d(num_features, num_features * 4, kernel_size=3, padding=1),
                    nn.PixelShuffle(2),
                    nn.Conv2d(num_features, num_channels, kernel_size=3, padding=1)
                )
            else:
                tail = nn.Sequential(
                    nn.Conv2d(num_features, num_channels * (scale ** 2), kernel_size=3, padding=1),
                    nn.PixelShuffle(scale)
                )
            
            self.tails[str(scale)] = tail
        
        self.current_scale = scale_factors[0]
    
    def set_scale(self, scale_factor):
        """Set the current scale factor"""
        if scale_factor in self.scale_factors:
            self.current_scale = scale_factor
        else:
            raise ValueError(f"Scale {scale_factor} not supported. Available: {self.scale_factors}")
    
    def forward(self, x):
        # Head
        x = self.head(x)
        residual = x
        
        # Body
        for block in self.body:
            x = block(x)
        x = self.body_conv(x)
        x = x + residual
        
        # Scale-specific tail
        x = self.tails[str(self.current_scale)](x)
        
        return x

# Model implementations

class SRCNNModel(BaseModel):
    """SRCNN Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            self.model = SRCNNNet(scale_factor=self.config.scale_factor)
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                self.model.load_state_dict(state_dict)
                logger.info(f"Loaded SRCNN weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for SRCNN, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            # Calculate memory usage
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading SRCNN model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            # Preprocess
            input_tensor = self.preprocess(image)
            
            # Inference
            output_tensor = self.model(input_tensor)
            
            # Postprocess
            result = self.postprocess(output_tensor)
            
            return result

class VDSRModel(BaseModel):
    """VDSR Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            self.model = VDSRNet(scale_factor=self.config.scale_factor)
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                self.model.load_state_dict(state_dict)
                logger.info(f"Loaded VDSR weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for VDSR, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading VDSR model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            input_tensor = self.preprocess(image)
            output_tensor = self.model(input_tensor)
            result = self.postprocess(output_tensor)
            return result

class EDSRModel(BaseModel):
    """EDSR Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            self.model = EDSRNet(scale_factor=self.config.scale_factor)
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                self.model.load_state_dict(state_dict)
                logger.info(f"Loaded EDSR weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for EDSR, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading EDSR model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            input_tensor = self.preprocess(image)
            output_tensor = self.model(input_tensor)
            result = self.postprocess(output_tensor)
            return result

class MDSRModel(BaseModel):
    """MDSR Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            self.model = MDSRNet(scale_factors=[2, 3, 4])
            self.model.set_scale(self.config.scale_factor)
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                self.model.load_state_dict(state_dict)
                logger.info(f"Loaded MDSR weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for MDSR, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading MDSR model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            input_tensor = self.preprocess(image)
            output_tensor = self.model(input_tensor)
            result = self.postprocess(output_tensor)
            return result
