"""
Waifu2x Model Implementations
All variants of Waifu2x for anime-style image upscaling with ROCm support
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import logging
from typing import Optional

from .model_manager import BaseModel
from config import ModelConfig

logger = logging.getLogger(__name__)

class Waifu2xCUNet(nn.Module):
    """Waifu2x CUNet architecture"""
    
    def __init__(self, in_channels=3, out_channels=3, num_features=32):
        super(Waifu2xCUNet, self).__init__()
        
        # Initial convolution
        self.conv1 = nn.Conv2d(in_channels, num_features, 3, 1, 1)
        
        # CUNet blocks
        self.conv2 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv3 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv4 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv5 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv6 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv7 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        
        # Output convolution
        self.conv_out = nn.Conv2d(num_features, out_channels, 3, 1, 1)
        
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        # Store input for residual connection
        residual = x
        
        # Feature extraction
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.relu(self.conv3(x))
        x = self.relu(self.conv4(x))
        x = self.relu(self.conv5(x))
        x = self.relu(self.conv6(x))
        x = self.relu(self.conv7(x))
        
        # Output
        x = self.conv_out(x)
        
        # Residual connection
        return x + residual

class Waifu2xUpConv(nn.Module):
    """Waifu2x UpConv architecture"""
    
    def __init__(self, in_channels=3, out_channels=3, scale_factor=2, num_features=16):
        super(Waifu2xUpConv, self).__init__()
        self.scale_factor = scale_factor
        
        # Feature extraction
        self.conv1 = nn.Conv2d(in_channels, num_features, 3, 1, 1)
        self.conv2 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv3 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv4 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv5 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv6 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        self.conv7 = nn.Conv2d(num_features, num_features, 3, 1, 1)
        
        # Upsampling
        if scale_factor == 2:
            self.upconv = nn.ConvTranspose2d(num_features, num_features, 4, 2, 1)
        else:
            # For other scales, use pixel shuffle
            self.upconv = nn.Conv2d(num_features, num_features * (scale_factor ** 2), 3, 1, 1)
            self.pixel_shuffle = nn.PixelShuffle(scale_factor)
        
        # Output
        self.conv_out = nn.Conv2d(num_features, out_channels, 3, 1, 1)
        
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        # Feature extraction
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.relu(self.conv3(x))
        x = self.relu(self.conv4(x))
        x = self.relu(self.conv5(x))
        x = self.relu(self.conv6(x))
        x = self.relu(self.conv7(x))
        
        # Upsampling
        if self.scale_factor == 2:
            x = self.relu(self.upconv(x))
        else:
            x = self.upconv(x)
            x = self.pixel_shuffle(x)
            x = self.relu(x)
        
        # Output
        x = self.conv_out(x)
        
        return x

class Waifu2xVGG(nn.Module):
    """Waifu2x VGG-style architecture"""
    
    def __init__(self, in_channels=3, out_channels=3, num_features=64):
        super(Waifu2xVGG, self).__init__()
        
        # VGG-style blocks
        self.features = nn.Sequential(
            # Block 1
            nn.Conv2d(in_channels, num_features, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(num_features, num_features, 3, 1, 1),
            nn.ReLU(inplace=True),
            
            # Block 2
            nn.Conv2d(num_features, num_features * 2, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(num_features * 2, num_features * 2, 3, 1, 1),
            nn.ReLU(inplace=True),
            
            # Block 3
            nn.Conv2d(num_features * 2, num_features * 4, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(num_features * 4, num_features * 4, 3, 1, 1),
            nn.ReLU(inplace=True),
            
            # Reconstruction
            nn.Conv2d(num_features * 4, num_features * 2, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(num_features * 2, num_features, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(num_features, out_channels, 3, 1, 1)
        )
    
    def forward(self, x):
        return self.features(x)

# Model implementations

class Waifu2xModel(BaseModel):
    """Standard Waifu2x Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            # Determine architecture based on config
            if "vgg" in self.config.name.lower():
                self.model = Waifu2xVGG()
            else:
                self.model = Waifu2xCUNet()
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                
                # Handle different state dict formats
                if isinstance(state_dict, dict) and 'model' in state_dict:
                    state_dict = state_dict['model']
                
                self.model.load_state_dict(state_dict, strict=False)
                logger.info(f"Loaded Waifu2x weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for Waifu2x, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            # Enable mixed precision if configured
            if self.config.precision == "fp16" and self.device.type == "cuda":
                self.model = self.model.half()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading Waifu2x model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            # Handle tiling for large images
            if max(image.shape[:2]) > self.config.tile_size:
                return self._upscale_tiled(image)
            
            # Preprocess
            input_tensor = self.preprocess(image)
            
            # Convert to half precision if needed
            if self.config.precision == "fp16" and self.device.type == "cuda":
                input_tensor = input_tensor.half()
            
            # Apply bicubic upsampling first (Waifu2x typically works on pre-upsampled images)
            if self.config.scale_factor > 1:
                h, w = input_tensor.shape[2:]
                new_h, new_w = h * self.config.scale_factor, w * self.config.scale_factor
                input_tensor = F.interpolate(input_tensor, size=(new_h, new_w), mode='bicubic', align_corners=False)
            
            # Inference
            output_tensor = self.model(input_tensor)
            
            # Postprocess
            result = self.postprocess(output_tensor)
            
            return result
    
    def _upscale_tiled(self, image: np.ndarray) -> np.ndarray:
        """Upscale large images using tiling"""
        h, w = image.shape[:2]
        tile_size = self.config.tile_size
        overlap = self.config.tile_overlap
        scale = self.config.scale_factor
        
        # Calculate output dimensions
        out_h, out_w = h * scale, w * scale
        output = np.zeros((out_h, out_w, 3), dtype=np.uint8)
        
        # Process tiles
        for y in range(0, h, tile_size - overlap):
            for x in range(0, w, tile_size - overlap):
                # Extract tile
                y_end = min(y + tile_size, h)
                x_end = min(x + tile_size, w)
                tile = image[y:y_end, x:x_end]
                
                # Upscale tile
                with torch.no_grad():
                    input_tensor = self.preprocess(tile)
                    
                    # Apply bicubic upsampling
                    if scale > 1:
                        th, tw = input_tensor.shape[2:]
                        new_th, new_tw = th * scale, tw * scale
                        input_tensor = F.interpolate(input_tensor, size=(new_th, new_tw), mode='bicubic', align_corners=False)
                    
                    if self.config.precision == "fp16" and self.device.type == "cuda":
                        input_tensor = input_tensor.half()
                    
                    output_tensor = self.model(input_tensor)
                    upscaled_tile = self.postprocess(output_tensor)
                
                # Place in output
                out_y = y * scale
                out_x = x * scale
                out_y_end = min(out_y + upscaled_tile.shape[0], out_h)
                out_x_end = min(out_x + upscaled_tile.shape[1], out_w)
                
                output[out_y:out_y_end, out_x:out_x_end] = upscaled_tile[:out_y_end-out_y, :out_x_end-out_x]
        
        return output

class Waifu2xCunetModel(BaseModel):
    """Waifu2x CUNet Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            self.model = Waifu2xCUNet()
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                
                if isinstance(state_dict, dict) and 'model' in state_dict:
                    state_dict = state_dict['model']
                
                self.model.load_state_dict(state_dict, strict=False)
                logger.info(f"Loaded Waifu2x CUNet weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for Waifu2x CUNet, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            if self.config.precision == "fp16" and self.device.type == "cuda":
                self.model = self.model.half()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading Waifu2x CUNet model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            if max(image.shape[:2]) > self.config.tile_size:
                return self._upscale_tiled(image)
            
            input_tensor = self.preprocess(image)
            
            # CUNet works on original resolution, then upsamples
            if self.config.precision == "fp16" and self.device.type == "cuda":
                input_tensor = input_tensor.half()
            
            # Denoise first
            denoised = self.model(input_tensor)
            
            # Then upscale
            if self.config.scale_factor > 1:
                h, w = denoised.shape[2:]
                new_h, new_w = h * self.config.scale_factor, w * self.config.scale_factor
                output_tensor = F.interpolate(denoised, size=(new_h, new_w), mode='bicubic', align_corners=False)
            else:
                output_tensor = denoised
            
            result = self.postprocess(output_tensor)
            return result
    
    def _upscale_tiled(self, image: np.ndarray) -> np.ndarray:
        """Upscale large images using tiling (similar to base Waifu2x)"""
        h, w = image.shape[:2]
        tile_size = self.config.tile_size
        overlap = self.config.tile_overlap
        scale = self.config.scale_factor
        
        out_h, out_w = h * scale, w * scale
        output = np.zeros((out_h, out_w, 3), dtype=np.uint8)
        
        for y in range(0, h, tile_size - overlap):
            for x in range(0, w, tile_size - overlap):
                y_end = min(y + tile_size, h)
                x_end = min(x + tile_size, w)
                tile = image[y:y_end, x:x_end]
                
                with torch.no_grad():
                    input_tensor = self.preprocess(tile)
                    
                    if self.config.precision == "fp16" and self.device.type == "cuda":
                        input_tensor = input_tensor.half()
                    
                    denoised = self.model(input_tensor)
                    
                    if scale > 1:
                        th, tw = denoised.shape[2:]
                        new_th, new_tw = th * scale, tw * scale
                        output_tensor = F.interpolate(denoised, size=(new_th, new_tw), mode='bicubic', align_corners=False)
                    else:
                        output_tensor = denoised
                    
                    upscaled_tile = self.postprocess(output_tensor)
                
                out_y = y * scale
                out_x = x * scale
                out_y_end = min(out_y + upscaled_tile.shape[0], out_h)
                out_x_end = min(out_x + upscaled_tile.shape[1], out_w)
                
                output[out_y:out_y_end, out_x:out_x_end] = upscaled_tile[:out_y_end-out_y, :out_x_end-out_x]
        
        return output

class Waifu2xUpconvModel(BaseModel):
    """Waifu2x UpConv Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            self.model = Waifu2xUpConv(scale_factor=self.config.scale_factor)
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                
                if isinstance(state_dict, dict) and 'model' in state_dict:
                    state_dict = state_dict['model']
                
                self.model.load_state_dict(state_dict, strict=False)
                logger.info(f"Loaded Waifu2x UpConv weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for Waifu2x UpConv, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            if self.config.precision == "fp16" and self.device.type == "cuda":
                self.model = self.model.half()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading Waifu2x UpConv model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            if max(image.shape[:2]) > self.config.tile_size:
                return self._upscale_tiled(image)
            
            input_tensor = self.preprocess(image)
            
            if self.config.precision == "fp16" and self.device.type == "cuda":
                input_tensor = input_tensor.half()
            
            # UpConv does upsampling internally
            output_tensor = self.model(input_tensor)
            result = self.postprocess(output_tensor)
            
            return result
    
    def _upscale_tiled(self, image: np.ndarray) -> np.ndarray:
        """Upscale large images using tiling"""
        h, w = image.shape[:2]
        tile_size = self.config.tile_size
        overlap = self.config.tile_overlap
        scale = self.config.scale_factor
        
        out_h, out_w = h * scale, w * scale
        output = np.zeros((out_h, out_w, 3), dtype=np.uint8)
        
        for y in range(0, h, tile_size - overlap):
            for x in range(0, w, tile_size - overlap):
                y_end = min(y + tile_size, h)
                x_end = min(x + tile_size, w)
                tile = image[y:y_end, x:x_end]
                
                with torch.no_grad():
                    input_tensor = self.preprocess(tile)
                    
                    if self.config.precision == "fp16" and self.device.type == "cuda":
                        input_tensor = input_tensor.half()
                    
                    output_tensor = self.model(input_tensor)
                    upscaled_tile = self.postprocess(output_tensor)
                
                out_y = y * scale
                out_x = x * scale
                out_y_end = min(out_y + upscaled_tile.shape[0], out_h)
                out_x_end = min(out_x + upscaled_tile.shape[1], out_w)
                
                output[out_y:out_y_end, out_x:out_x_end] = upscaled_tile[:out_y_end-out_y, :out_x_end-out_x]
        
        return output
