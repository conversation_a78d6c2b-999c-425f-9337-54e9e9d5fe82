"""
Comprehensive AI Model Manager
Supports ALL available upscaling models with ROCm acceleration
"""

import os
import torch
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple, Any
from abc import ABC, abstractmethod
import logging
from concurrent.futures import ThreadPoolExecutor
import asyncio
from dataclasses import dataclass

from config import config, UpscalingModel, ModelConfig

# Import all model implementations - will be created
try:
    from .traditional_sr import SRCNNModel, VDSRModel, EDSRModel, MDSRModel
except ImportError:
    SRCNNModel = VDSRModel = EDSRModel = MDSRModel = None

try:
    from .esrgan_models import ESRGANModel, RealESRGANModel
except ImportError:
    ESRGANModel = RealESRGANModel = None

try:
    from .waifu2x_models import Waifu2xModel, Waifu2xCunetModel, Waifu2xUpconvModel
except ImportError:
    Waifu2xModel = Waifu2xCunetModel = Waifu2xUpconvModel = None

try:
    from .attention_models import RCANModel, SANModel, HANModel
except ImportError:
    RCANModel = SANModel = HANModel = None

try:
    from .advanced_sr import SRGANModel, FSRCNNModel, LapSRNModel, DRCNModel, DRRNModel
except ImportError:
    SRGANModel = FSRCNNModel = LapSRNModel = DRCNModel = DRRNModel = None

try:
    from .transformer_models import SwinIRModel, HATModel, Swin2SRModel
except ImportError:
    SwinIRModel = HATModel = Swin2SRModel = None

try:
    from .diffusion_models import LDSRModel, StableDiffusionUpscaleModel
except ImportError:
    LDSRModel = StableDiffusionUpscaleModel = None

try:
    from .video_models import BasicVSRModel, BasicVSRPlusModel, IconVSRModel, TDANModel, EDVRModel
except ImportError:
    BasicVSRModel = BasicVSRPlusModel = IconVSRModel = TDANModel = EDVRModel = None

try:
    from .realtime_models import RealtimeESRGANModel, FastSRGANModel, EfficientSRModel
except ImportError:
    RealtimeESRGANModel = FastSRGANModel = EfficientSRModel = None

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    """Information about a loaded model"""
    name: str
    model_type: str
    scale_factor: int
    input_channels: int
    output_channels: int
    memory_usage: float
    inference_time: float
    is_loaded: bool = False
    supports_batch: bool = True
    supports_video: bool = False

class BaseModel(ABC):
    """Abstract base class for all upscaling models"""

    def __init__(self, config: ModelConfig, device: torch.device):
        self.config = config
        self.device = device
        self.model = None
        self.is_loaded = False
        self.memory_usage = 0.0

    @abstractmethod
    def load_model(self) -> bool:
        """Load the model weights"""
        pass

    @abstractmethod
    def unload_model(self):
        """Unload model from memory"""
        pass

    @abstractmethod
    def upscale(self, image: np.ndarray) -> np.ndarray:
        """Upscale a single image"""
        pass

    def upscale_batch(self, images: List[np.ndarray]) -> List[np.ndarray]:
        """Upscale a batch of images (default implementation)"""
        return [self.upscale(img) for img in images]

    def preprocess(self, image: np.ndarray) -> torch.Tensor:
        """Preprocess image for model input"""
        # Convert to RGB if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = image[:, :, ::-1]  # BGR to RGB

        # Normalize to [0, 1]
        image = image.astype(np.float32) / 255.0

        # Convert to tensor and add batch dimension
        tensor = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0)

        return tensor.to(self.device)

    def postprocess(self, tensor: torch.Tensor) -> np.ndarray:
        """Postprocess model output to image"""
        # Remove batch dimension and convert to numpy
        image = tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()

        # Clamp and convert to uint8
        image = np.clip(image * 255.0, 0, 255).astype(np.uint8)

        # Convert RGB to BGR if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = image[:, :, ::-1]

        return image

class ModelManager:
    """Comprehensive manager for ALL AI upscaling models"""

    def __init__(self):
        self.device = self._setup_device()
        self.loaded_models: Dict[str, BaseModel] = {}
        self.model_info: Dict[str, ModelInfo] = {}
        self.model_classes = self._register_all_models()
        self.executor = ThreadPoolExecutor(max_workers=4)

        # Initialize model info
        self._initialize_model_info()

        logger.info(f"ModelManager initialized with device: {self.device}")
        logger.info(f"Registered {len(self.model_classes)} model types")

    def _setup_device(self) -> torch.device:
        """Setup ROCm device for AMD GPU acceleration"""
        if torch.cuda.is_available():
            # Check if ROCm is available
            device_count = torch.cuda.device_count()
            device_id = min(config.rocm.device_id, device_count - 1)
            device = torch.device(f"cuda:{device_id}")

            # Set memory fraction
            if hasattr(torch.cuda, 'set_memory_fraction'):
                torch.cuda.set_memory_fraction(config.rocm.memory_fraction, device_id)

            logger.info(f"Using ROCm device: {device}")
            return device
        else:
            logger.warning("ROCm/CUDA not available, using CPU")
            return torch.device("cpu")

    def _register_all_models(self) -> Dict[str, type]:
        """Register ALL supported model classes"""
        models = {}

        # Helper function to safely add models
        def add_model(enum_val, model_class):
            if model_class is not None:
                models[enum_val.value] = model_class

        # Traditional SR Models
        add_model(UpscalingModel.SRCNN, SRCNNModel)
        add_model(UpscalingModel.VDSR, VDSRModel)
        add_model(UpscalingModel.EDSR, EDSRModel)
        add_model(UpscalingModel.MDSR, MDSRModel)

        # ESRGAN Family
        add_model(UpscalingModel.ESRGAN, ESRGANModel)
        add_model(UpscalingModel.REAL_ESRGAN, RealESRGANModel)
        add_model(UpscalingModel.REAL_ESRGAN_X4, RealESRGANModel)
        add_model(UpscalingModel.REAL_ESRGAN_ANIME, RealESRGANModel)

        # Waifu2x Variants
        add_model(UpscalingModel.WAIFU2X, Waifu2xModel)
        add_model(UpscalingModel.WAIFU2X_CUNET, Waifu2xCunetModel)
        add_model(UpscalingModel.WAIFU2X_UPCONV, Waifu2xUpconvModel)
        add_model(UpscalingModel.WAIFU2X_ANIME_STYLE, Waifu2xModel)

        # Attention Models
        add_model(UpscalingModel.RCAN, RCANModel)
        add_model(UpscalingModel.SAN, SANModel)
        add_model(UpscalingModel.HAN, HANModel)

        # Advanced Models
        add_model(UpscalingModel.SRGAN, SRGANModel)
        add_model(UpscalingModel.FSRCNN, FSRCNNModel)
        add_model(UpscalingModel.LapSRN, LapSRNModel)
        add_model(UpscalingModel.DRCN, DRCNModel)
        add_model(UpscalingModel.DRRN, DRRNModel)

        # Transformer Models
        add_model(UpscalingModel.SwinIR, SwinIRModel)
        add_model(UpscalingModel.HAT, HATModel)
        add_model(UpscalingModel.SWIN2SR, Swin2SRModel)

        # Diffusion Models
        add_model(UpscalingModel.LDSR, LDSRModel)
        add_model(UpscalingModel.STABLE_DIFFUSION_UPSCALE, StableDiffusionUpscaleModel)

        # Video Models
        add_model(UpscalingModel.BASICVSR, BasicVSRModel)
        add_model(UpscalingModel.BASICVSR_PLUS, BasicVSRPlusModel)
        add_model(UpscalingModel.ICONVSR, IconVSRModel)
        add_model(UpscalingModel.TDAN, TDANModel)
        add_model(UpscalingModel.EDVR, EDVRModel)

        # Real-time Models
        add_model(UpscalingModel.REALTIME_ESRGAN, RealtimeESRGANModel)
        add_model(UpscalingModel.FAST_SRGAN, FastSRGANModel)
        add_model(UpscalingModel.EFFICIENT_SR, EfficientSRModel)

        return models

    def _initialize_model_info(self):
        """Initialize information for all models"""
        for model_name, model_class in self.model_classes.items():
            if model_name in config.models and model_class is not None:
                model_config = config.models[model_name]

                # Create model info
                self.model_info[model_name] = ModelInfo(
                    name=model_name,
                    model_type=model_class.__name__,
                    scale_factor=model_config.scale_factor,
                    input_channels=3,  # Default RGB
                    output_channels=3,  # Default RGB
                    memory_usage=0.0,
                    inference_time=0.0,
                    is_loaded=False,
                    supports_batch=hasattr(model_class, 'upscale_batch'),
                    supports_video="video" in model_name.lower() or "vsr" in model_name.lower()
                )

    def get_available_models(self) -> List[str]:
        """Get list of models with available weights"""
        available = []
        for model_name in self.model_classes.keys():
            if model_name in config.models:
                model_config = config.models[model_name]
                if model_config.model_path and Path(model_config.model_path).exists():
                    available.append(model_name)
        return available

    def get_all_models(self) -> List[str]:
        """Get list of all supported models"""
        return list(self.model_classes.keys())

    def get_loaded_models(self) -> List[str]:
        """Get list of currently loaded models"""
        return list(self.loaded_models.keys())

    def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """Get information about a specific model"""
        return self.model_info.get(model_name)

    async def load_model(self, model_name: str) -> bool:
        """Load a specific model asynchronously"""
        if model_name in self.loaded_models:
            logger.info(f"Model {model_name} already loaded")
            return True

        if model_name not in self.model_classes:
            logger.error(f"Unknown model: {model_name}")
            return False

        if model_name not in config.models:
            logger.error(f"No configuration for model: {model_name}")
            return False

        try:
            model_config = config.models[model_name]
            model_class = self.model_classes[model_name]

            # Create model instance
            model = model_class(model_config, self.device)

            # Load model weights in executor to avoid blocking
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(self.executor, model.load_model)

            if success:
                self.loaded_models[model_name] = model
                if model_name in self.model_info:
                    self.model_info[model_name].is_loaded = True
                    self.model_info[model_name].memory_usage = model.memory_usage

                logger.info(f"Successfully loaded model: {model_name}")
                return True
            else:
                logger.error(f"Failed to load model: {model_name}")
                return False

        except Exception as e:
            logger.error(f"Error loading model {model_name}: {e}")
            return False

    def unload_model(self, model_name: str):
        """Unload a specific model"""
        if model_name in self.loaded_models:
            self.loaded_models[model_name].unload_model()
            del self.loaded_models[model_name]

            if model_name in self.model_info:
                self.model_info[model_name].is_loaded = False
                self.model_info[model_name].memory_usage = 0.0

            logger.info(f"Unloaded model: {model_name}")

    def unload_all_models(self):
        """Unload all loaded models"""
        for model_name in list(self.loaded_models.keys()):
            self.unload_model(model_name)

    async def upscale_image(self, image: np.ndarray, model_name: str) -> Optional[np.ndarray]:
        """Upscale an image using the specified model"""
        if model_name not in self.loaded_models:
            logger.error(f"Model {model_name} not loaded")
            return None

        try:
            model = self.loaded_models[model_name]

            # Run upscaling in executor to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(self.executor, model.upscale, image)

            return result

        except Exception as e:
            logger.error(f"Error upscaling with model {model_name}: {e}")
            return None

    async def upscale_batch(self, images: List[np.ndarray], model_name: str) -> List[np.ndarray]:
        """Upscale a batch of images using the specified model"""
        if model_name not in self.loaded_models:
            logger.error(f"Model {model_name} not loaded")
            return []

        try:
            model = self.loaded_models[model_name]

            # Check if model supports batch processing
            if hasattr(model, 'upscale_batch') and self.model_info[model_name].supports_batch:
                loop = asyncio.get_event_loop()
                results = await loop.run_in_executor(self.executor, model.upscale_batch, images)
            else:
                # Process individually
                results = []
                for image in images:
                    result = await self.upscale_image(image, model_name)
                    if result is not None:
                        results.append(result)

            return results

        except Exception as e:
            logger.error(f"Error batch upscaling with model {model_name}: {e}")
            return []

    def get_memory_usage(self) -> Dict[str, float]:
        """Get memory usage for all loaded models"""
        return {name: info.memory_usage for name, info in self.model_info.items() if info.is_loaded}

    def get_total_memory_usage(self) -> float:
        """Get total memory usage of all loaded models"""
        return sum(self.get_memory_usage().values())

# Global model manager instance
model_manager = ModelManager()
