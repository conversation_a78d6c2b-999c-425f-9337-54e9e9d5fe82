"""
ESRGAN and Real-ESRGAN Models
Enhanced Super-Resolution Generative Adversarial Networks with ROCm support
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import logging
from typing import Optional
import math

from .model_manager import BaseModel
from config import ModelConfig

logger = logging.getLogger(__name__)

class ResidualDenseBlock(nn.Module):
    """Residual Dense Block for ESRGAN"""
    
    def __init__(self, num_features=64, num_grow_ch=32):
        super(ResidualDenseBlock, self).__init__()
        self.conv1 = nn.Conv2d(num_features, num_grow_ch, 3, 1, 1)
        self.conv2 = nn.Conv2d(num_features + num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv3 = nn.Conv2d(num_features + 2 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv4 = nn.Conv2d(num_features + 3 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv5 = nn.Conv2d(num_features + 4 * num_grow_ch, num_features, 3, 1, 1)
        
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)
        
        # Initialization
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in [self.conv1, self.conv2, self.conv3, self.conv4]:
            nn.init.kaiming_normal_(m.weight, a=0.2, mode='fan_in', nonlinearity='leaky_relu')
            nn.init.constant_(m.bias, 0)
        nn.init.kaiming_normal_(self.conv5.weight, a=0, mode='fan_in', nonlinearity='linear')
        nn.init.constant_(self.conv5.bias, 0)
    
    def forward(self, x):
        x1 = self.lrelu(self.conv1(x))
        x2 = self.lrelu(self.conv2(torch.cat((x, x1), 1)))
        x3 = self.lrelu(self.conv3(torch.cat((x, x1, x2), 1)))
        x4 = self.lrelu(self.conv4(torch.cat((x, x1, x2, x3), 1)))
        x5 = self.conv5(torch.cat((x, x1, x2, x3, x4), 1))
        
        # Empirical scaling factor 0.2
        return x5 * 0.2 + x

class RRDB(nn.Module):
    """Residual in Residual Dense Block"""
    
    def __init__(self, num_features, num_grow_ch=32):
        super(RRDB, self).__init__()
        self.rdb1 = ResidualDenseBlock(num_features, num_grow_ch)
        self.rdb2 = ResidualDenseBlock(num_features, num_grow_ch)
        self.rdb3 = ResidualDenseBlock(num_features, num_grow_ch)
    
    def forward(self, x):
        out = self.rdb1(x)
        out = self.rdb2(out)
        out = self.rdb3(out)
        
        # Empirical scaling factor 0.2
        return out * 0.2 + x

class ESRGANNet(nn.Module):
    """ESRGAN Generator Network"""
    
    def __init__(self, num_in_ch=3, num_out_ch=3, scale=4, num_feat=64, num_block=23, num_grow_ch=32):
        super(ESRGANNet, self).__init__()
        self.scale = scale
        
        # First convolution
        self.conv_first = nn.Conv2d(num_in_ch, num_feat, 3, 1, 1)
        
        # Body
        self.body = nn.ModuleList([RRDB(num_feat, num_grow_ch) for _ in range(num_block)])
        self.conv_body = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        
        # Upsampling
        self.conv_up1 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_up2 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_hr = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_last = nn.Conv2d(num_feat, num_out_ch, 3, 1, 1)
        
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)
    
    def forward(self, x):
        feat = self.conv_first(x)
        body_feat = feat
        
        for block in self.body:
            body_feat = block(body_feat)
        
        body_feat = self.conv_body(body_feat)
        feat = feat + body_feat
        
        # Upsampling
        feat = self.lrelu(self.conv_up1(F.interpolate(feat, scale_factor=2, mode='nearest')))
        feat = self.lrelu(self.conv_up2(F.interpolate(feat, scale_factor=2, mode='nearest')))
        
        out = self.conv_last(self.lrelu(self.conv_hr(feat)))
        
        return out

class RealESRGANNet(nn.Module):
    """Real-ESRGAN Network with additional improvements"""
    
    def __init__(self, num_in_ch=3, num_out_ch=3, scale=4, num_feat=64, num_block=23, num_grow_ch=32):
        super(RealESRGANNet, self).__init__()
        self.scale = scale
        
        # First convolution
        self.conv_first = nn.Conv2d(num_in_ch, num_feat, 3, 1, 1)
        
        # Body
        self.body = nn.ModuleList([RRDB(num_feat, num_grow_ch) for _ in range(num_block)])
        self.conv_body = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        
        # Upsampling layers
        if scale == 2:
            self.upconv1 = nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1)
            self.pixel_shuffle1 = nn.PixelShuffle(2)
        elif scale == 4:
            self.upconv1 = nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1)
            self.pixel_shuffle1 = nn.PixelShuffle(2)
            self.upconv2 = nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1)
            self.pixel_shuffle2 = nn.PixelShuffle(2)
        elif scale == 8:
            self.upconv1 = nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1)
            self.pixel_shuffle1 = nn.PixelShuffle(2)
            self.upconv2 = nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1)
            self.pixel_shuffle2 = nn.PixelShuffle(2)
            self.upconv3 = nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1)
            self.pixel_shuffle3 = nn.PixelShuffle(2)
        
        self.conv_hr = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_last = nn.Conv2d(num_feat, num_out_ch, 3, 1, 1)
        
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)
    
    def forward(self, x):
        feat = self.conv_first(x)
        body_feat = feat
        
        for block in self.body:
            body_feat = block(body_feat)
        
        body_feat = self.conv_body(body_feat)
        feat = feat + body_feat
        
        # Upsampling
        if self.scale >= 2:
            feat = self.lrelu(self.pixel_shuffle1(self.upconv1(feat)))
        if self.scale >= 4:
            feat = self.lrelu(self.pixel_shuffle2(self.upconv2(feat)))
        if self.scale >= 8:
            feat = self.lrelu(self.pixel_shuffle3(self.upconv3(feat)))
        
        out = self.conv_last(self.lrelu(self.conv_hr(feat)))
        
        return out

class SRVGGNetCompact(nn.Module):
    """Compact VGG-style network for Real-ESRGAN"""
    
    def __init__(self, num_in_ch=3, num_out_ch=3, num_feat=64, num_conv=16, upscale=4, act_type='prelu'):
        super(SRVGGNetCompact, self).__init__()
        self.num_in_ch = num_in_ch
        self.num_out_ch = num_out_ch
        self.num_feat = num_feat
        self.num_conv = num_conv
        self.upscale = upscale
        self.act_type = act_type
        
        self.body = nn.ModuleList()
        # First conv
        self.body.append(nn.Conv2d(num_in_ch, num_feat, 3, 1, 1))
        if act_type == 'relu':
            activation = nn.ReLU(inplace=True)
        elif act_type == 'prelu':
            activation = nn.PReLU(num_parameters=num_feat)
        elif act_type == 'leakyrelu':
            activation = nn.LeakyReLU(negative_slope=0.1, inplace=True)
        self.body.append(activation)
        
        # Body convs
        for _ in range(num_conv):
            self.body.append(nn.Conv2d(num_feat, num_feat, 3, 1, 1))
            if act_type == 'relu':
                activation = nn.ReLU(inplace=True)
            elif act_type == 'prelu':
                activation = nn.PReLU(num_parameters=num_feat)
            elif act_type == 'leakyrelu':
                activation = nn.LeakyReLU(negative_slope=0.1, inplace=True)
            self.body.append(activation)
        
        # Upsampling
        if upscale == 2:
            self.body.append(nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1))
            self.body.append(nn.PixelShuffle(2))
        elif upscale == 4:
            self.body.append(nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1))
            self.body.append(nn.PixelShuffle(2))
            self.body.append(nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1))
            self.body.append(nn.PixelShuffle(2))
        
        # Final conv
        self.body.append(nn.Conv2d(num_feat, num_out_ch, 3, 1, 1))
    
    def forward(self, x):
        for layer in self.body:
            x = layer(x)
        return x

# Model implementations

class ESRGANModel(BaseModel):
    """ESRGAN Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            self.model = ESRGANNet(scale=self.config.scale_factor)
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                
                # Handle different state dict formats
                if 'params_ema' in state_dict:
                    state_dict = state_dict['params_ema']
                elif 'params' in state_dict:
                    state_dict = state_dict['params']
                
                self.model.load_state_dict(state_dict, strict=False)
                logger.info(f"Loaded ESRGAN weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for ESRGAN, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            # Enable mixed precision if configured
            if self.config.precision == "fp16" and self.device.type == "cuda":
                self.model = self.model.half()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading ESRGAN model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            # Handle tiling for large images
            if max(image.shape[:2]) > self.config.tile_size:
                return self._upscale_tiled(image)
            
            # Preprocess
            input_tensor = self.preprocess(image)
            
            # Convert to half precision if needed
            if self.config.precision == "fp16" and self.device.type == "cuda":
                input_tensor = input_tensor.half()
            
            # Inference
            output_tensor = self.model(input_tensor)
            
            # Postprocess
            result = self.postprocess(output_tensor)
            
            return result
    
    def _upscale_tiled(self, image: np.ndarray) -> np.ndarray:
        """Upscale large images using tiling"""
        h, w = image.shape[:2]
        tile_size = self.config.tile_size
        overlap = self.config.tile_overlap
        scale = self.config.scale_factor
        
        # Calculate output dimensions
        out_h, out_w = h * scale, w * scale
        output = np.zeros((out_h, out_w, 3), dtype=np.uint8)
        
        # Process tiles
        for y in range(0, h, tile_size - overlap):
            for x in range(0, w, tile_size - overlap):
                # Extract tile
                y_end = min(y + tile_size, h)
                x_end = min(x + tile_size, w)
                tile = image[y:y_end, x:x_end]
                
                # Upscale tile
                with torch.no_grad():
                    input_tensor = self.preprocess(tile)
                    if self.config.precision == "fp16" and self.device.type == "cuda":
                        input_tensor = input_tensor.half()
                    output_tensor = self.model(input_tensor)
                    upscaled_tile = self.postprocess(output_tensor)
                
                # Place in output
                out_y = y * scale
                out_x = x * scale
                out_y_end = min(out_y + upscaled_tile.shape[0], out_h)
                out_x_end = min(out_x + upscaled_tile.shape[1], out_w)
                
                output[out_y:out_y_end, out_x:out_x_end] = upscaled_tile[:out_y_end-out_y, :out_x_end-out_x]
        
        return output

class RealESRGANModel(BaseModel):
    """Real-ESRGAN Model Implementation"""
    
    def load_model(self) -> bool:
        try:
            # Determine model architecture based on config
            if "anime" in self.config.name.lower():
                self.model = RealESRGANNet(scale=self.config.scale_factor, num_block=6)
            elif "compact" in self.config.name.lower():
                self.model = SRVGGNetCompact(upscale=self.config.scale_factor)
            else:
                self.model = RealESRGANNet(scale=self.config.scale_factor)
            
            if self.config.model_path and Path(self.config.model_path).exists():
                state_dict = torch.load(self.config.model_path, map_location=self.device)
                
                # Handle different state dict formats
                if 'params_ema' in state_dict:
                    state_dict = state_dict['params_ema']
                elif 'params' in state_dict:
                    state_dict = state_dict['params']
                
                self.model.load_state_dict(state_dict, strict=False)
                logger.info(f"Loaded Real-ESRGAN weights from {self.config.model_path}")
            else:
                logger.warning("No weights found for Real-ESRGAN, using random initialization")
            
            self.model.to(self.device)
            self.model.eval()
            
            # Enable mixed precision if configured
            if self.config.precision == "fp16" and self.device.type == "cuda":
                self.model = self.model.half()
            
            self.memory_usage = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2
            self.is_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading Real-ESRGAN model: {e}")
            return False
    
    def unload_model(self):
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    def upscale(self, image: np.ndarray) -> np.ndarray:
        if not self.is_loaded:
            raise RuntimeError("Model not loaded")
        
        with torch.no_grad():
            # Handle tiling for large images
            if max(image.shape[:2]) > self.config.tile_size:
                return self._upscale_tiled(image)
            
            # Preprocess
            input_tensor = self.preprocess(image)
            
            # Convert to half precision if needed
            if self.config.precision == "fp16" and self.device.type == "cuda":
                input_tensor = input_tensor.half()
            
            # Inference
            output_tensor = self.model(input_tensor)
            
            # Postprocess
            result = self.postprocess(output_tensor)
            
            return result
    
    def _upscale_tiled(self, image: np.ndarray) -> np.ndarray:
        """Upscale large images using tiling (same as ESRGAN)"""
        h, w = image.shape[:2]
        tile_size = self.config.tile_size
        overlap = self.config.tile_overlap
        scale = self.config.scale_factor
        
        out_h, out_w = h * scale, w * scale
        output = np.zeros((out_h, out_w, 3), dtype=np.uint8)
        
        for y in range(0, h, tile_size - overlap):
            for x in range(0, w, tile_size - overlap):
                y_end = min(y + tile_size, h)
                x_end = min(x + tile_size, w)
                tile = image[y:y_end, x:x_end]
                
                with torch.no_grad():
                    input_tensor = self.preprocess(tile)
                    if self.config.precision == "fp16" and self.device.type == "cuda":
                        input_tensor = input_tensor.half()
                    output_tensor = self.model(input_tensor)
                    upscaled_tile = self.postprocess(output_tensor)
                
                out_y = y * scale
                out_x = x * scale
                out_y_end = min(out_y + upscaled_tile.shape[0], out_h)
                out_x_end = min(out_x + upscaled_tile.shape[1], out_w)
                
                output[out_y:out_y_end, out_x:out_x_end] = upscaled_tile[:out_y_end-out_y, :out_x_end-out_x]
        
        return output
