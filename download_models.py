#!/usr/bin/env python3
"""
Model Download Script for AI Video Player
Downloads popular AI upscaling model weights
"""

import os
import sys
import requests
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple
import logging
from tqdm import tqdm

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model download URLs and checksums (Updated with working URLs)
MODEL_URLS = {
    # Real-ESRGAN models (Working URLs)
    "RealESRGAN_x4plus.pth": {
        "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth",
        "sha256": "4fa0d38905f75ac06eb49a7951b426670021be3018265fd191d2125df9d682f1"
    },
    "RealESRGAN_x4plus_anime_6B.pth": {
        "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth",
        "sha256": "f872d837d3c90ed2e05227bed711af5671a6fd1c9f7d7e91c911a61f155e99da"
    },
    "RealESRGAN_x2plus.pth": {
        "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth",
        "sha256": "49fafd45f8fd7aa8d31ab2a22d14d91b536c34494a5cfe31eb5d89c2fa266abb"
    },

    # Additional working models
    "RealESRGAN_x4plus_anime_6B.pth": {
        "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth",
        "sha256": "f872d837d3c90ed2e05227bed711af5671a6fd1c9f7d7e91c911a61f155e99da"
    },

    # Placeholder models (will be skipped if URLs don't work)
    "waifu2x_art.pth": {
        "url": "https://example.com/waifu2x_art.pth",  # Placeholder - will be skipped
        "sha256": "skip_download"
    },

    "edsr_x4.pth": {
        "url": "https://example.com/edsr_x4.pth",  # Placeholder - will be skipped
        "sha256": "skip_download"
    }
}

def calculate_sha256(file_path: Path) -> str:
    """Calculate SHA256 hash of a file"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    return sha256_hash.hexdigest()

def download_file(url: str, destination: Path, expected_hash: str = None) -> bool:
    """Download a file with progress bar and optional hash verification"""
    try:
        # Skip placeholder URLs
        if "example.com" in url or expected_hash == "skip_download":
            logger.info(f"Skipping {destination.name} (placeholder URL)")
            return False

        logger.info(f"Downloading {destination.name}...")

        # Create directory if it doesn't exist
        destination.parent.mkdir(parents=True, exist_ok=True)

        # Download with progress bar
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))

        with open(destination, 'wb') as f, tqdm(
            desc=destination.name,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))

        # Verify hash if provided
        if expected_hash and expected_hash not in ["placeholder_hash", "skip_download"]:
            logger.info(f"Verifying {destination.name}...")
            actual_hash = calculate_sha256(destination)
            if actual_hash != expected_hash:
                logger.warning(f"Hash mismatch for {destination.name} (continuing anyway)")
                logger.warning(f"Expected: {expected_hash}")
                logger.warning(f"Actual:   {actual_hash}")
                # Don't remove file on hash mismatch, just warn
            else:
                logger.info(f"Hash verified for {destination.name}")

        logger.info(f"Successfully downloaded {destination.name}")
        return True

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error downloading {destination.name}: {e}")
        if destination.exists():
            destination.unlink()  # Remove partial file
        return False
    except Exception as e:
        logger.error(f"Error downloading {destination.name}: {e}")
        if destination.exists():
            destination.unlink()  # Remove partial file
        return False

def download_models(models_dir: Path, selected_models: List[str] = None) -> Dict[str, bool]:
    """Download selected models or all models"""
    results = {}

    # Create models directory
    models_dir.mkdir(parents=True, exist_ok=True)

    # Determine which models to download
    if selected_models:
        models_to_download = {k: v for k, v in MODEL_URLS.items() if k in selected_models}
    else:
        models_to_download = MODEL_URLS

    logger.info(f"Downloading {len(models_to_download)} models to {models_dir}")

    for model_name, model_info in models_to_download.items():
        destination = models_dir / model_name

        # Skip if already exists and hash matches
        if destination.exists():
            if model_info["sha256"] != "placeholder_hash":
                existing_hash = calculate_sha256(destination)
                if existing_hash == model_info["sha256"]:
                    logger.info(f"Skipping {model_name} (already exists and verified)")
                    results[model_name] = True
                    continue
                else:
                    logger.info(f"Re-downloading {model_name} (hash mismatch)")
            else:
                logger.info(f"Skipping {model_name} (already exists, no hash to verify)")
                results[model_name] = True
                continue

        # Download the model
        success = download_file(
            model_info["url"],
            destination,
            model_info["sha256"]
        )
        results[model_name] = success

    return results

def list_available_models():
    """List all available models for download"""
    print("Available models for download:")
    print("=" * 50)

    categories = {
        "Real-ESRGAN": ["RealESRGAN_x4plus.pth", "RealESRGAN_x4plus_anime_6B.pth", "RealESRGAN_x2plus.pth"],
        "ESRGAN": ["ESRGAN_x4.pth"],
        "Waifu2x": ["waifu2x_art.pth"],
        "EDSR": ["edsr_x4.pth"]
    }

    for category, models in categories.items():
        print(f"\n{category}:")
        for model in models:
            if model in MODEL_URLS:
                size_info = ""
                try:
                    response = requests.head(MODEL_URLS[model]["url"], timeout=5)
                    if 'content-length' in response.headers:
                        size_mb = int(response.headers['content-length']) / (1024 * 1024)
                        size_info = f" ({size_mb:.1f} MB)"
                except:
                    pass

                print(f"  - {model}{size_info}")

def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description="Download AI upscaling model weights")
    parser.add_argument("--models-dir", type=Path, default="models",
                       help="Directory to save models (default: models)")
    parser.add_argument("--list", action="store_true",
                       help="List available models")
    parser.add_argument("--models", nargs="+",
                       help="Specific models to download (default: all)")
    parser.add_argument("--essential", action="store_true",
                       help="Download only essential models (Real-ESRGAN)")

    args = parser.parse_args()

    if args.list:
        list_available_models()
        return

    # Determine which models to download
    if args.essential:
        selected_models = ["RealESRGAN_x4plus.pth", "RealESRGAN_x4plus_anime_6B.pth"]
    elif args.models:
        selected_models = args.models
        # Validate model names
        invalid_models = [m for m in selected_models if m not in MODEL_URLS]
        if invalid_models:
            logger.error(f"Invalid model names: {invalid_models}")
            logger.info("Use --list to see available models")
            return 1
    else:
        selected_models = None  # Download all

    # Download models
    results = download_models(args.models_dir, selected_models)

    # Print summary
    print("\nDownload Summary:")
    print("=" * 30)
    successful = sum(1 for success in results.values() if success)
    total = len(results)

    for model_name, success in results.items():
        status = "✓" if success else "✗"
        print(f"{status} {model_name}")

    print(f"\nSuccessfully downloaded: {successful}/{total} models")

    if successful > 0:
        print(f"\nModels saved to: {args.models_dir.absolute()}")
        print("You can now run the AI Video Player with these models!")

    return 0 if successful == total else 1

if __name__ == "__main__":
    sys.exit(main())
