models:
  basicvsr:
    anime_mode: false
    batch_size: 4
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: basicvsr
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 256
    use_gpu: true
  basicvsr_plus:
    anime_mode: false
    batch_size: 4
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: basicvsr_plus
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 256
    use_gpu: true
  drcn:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: drcn
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  drrn:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: drrn
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  edsr:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: edsr
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  edvr:
    anime_mode: false
    batch_size: 4
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: edvr
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 256
    use_gpu: true
  efficient_sr:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: efficient_sr
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  esrgan:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: esrgan
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  fast_srgan:
    anime_mode: false
    batch_size: 2
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: fast_srgan
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 256
    use_gpu: true
  fsrcnn:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: fsrcnn
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  han:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: han
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  hat:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: hat
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  iconvsr:
    anime_mode: false
    batch_size: 4
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: iconvsr
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 256
    use_gpu: true
  lapsrn:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: lapsrn
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  ldsr:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: ldsr
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  mdsr:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: mdsr
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  rcan:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: rcan
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  real_esrgan:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: real_esrgan
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  real_esrgan_anime:
    anime_mode: true
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: real_esrgan_anime
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  real_esrgan_x4:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: real_esrgan_x4
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  realtime_esrgan:
    anime_mode: false
    batch_size: 2
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: realtime_esrgan
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 256
    use_gpu: true
  san:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: san
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  sd_upscale:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: sd_upscale
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  srcnn:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: srcnn
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  srgan:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: srgan
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  swin2sr:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: swin2sr
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  swinir:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: swinir
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  tdan:
    anime_mode: false
    batch_size: 4
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: tdan
    precision: fp16
    scale_factor: 4
    tile_overlap: 32
    tile_size: 256
    use_gpu: true
  vdsr:
    anime_mode: false
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: vdsr
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  waifu2x:
    anime_mode: true
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: waifu2x
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  waifu2x_anime_style:
    anime_mode: true
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: waifu2x_anime_style
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  waifu2x_cunet:
    anime_mode: true
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: waifu2x_cunet
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
  waifu2x_upconv:
    anime_mode: true
    batch_size: 1
    denoise_strength: 0.5
    face_enhance: false
    model_path: null
    name: waifu2x_upconv
    precision: fp16
    scale_factor: 2
    tile_overlap: 32
    tile_size: 512
    use_gpu: true
rocm:
  allow_growth: true
  device_id: 0
  enable_profiling: false
  memory_fraction: 0.8
  optimize_for_inference: true
  use_mixed_precision: true
ui:
  auto_save_settings: true
  fullscreen: false
  language: en
  show_model_info: true
  show_performance_overlay: true
  theme: dark
  window_size:
  - 1280
  - 720
video:
  fps_target: null
  hardware_decode: true
  hardware_encode: true
  input_formats:
  - mp4
  - avi
  - mkv
  - mov
  - wmv
  - flv
  - webm
  - m4v
  - 3gp
  - ts
  - mts
  - m2ts
  - vob
  - ogv
  output_format: mp4
  quality_preset: high
