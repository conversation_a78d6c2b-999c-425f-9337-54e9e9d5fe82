"""
Download Manager for AI Models
Integrates with the GUI to provide real-time download progress and management
"""

import os
import sys
import subprocess
import threading
import logging
from pathlib import Path
from typing import Dict, List, Optional, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt6.QtWidgets import QMessageBox

logger = logging.getLogger(__name__)

class DownloadWorker(QThread):
    """Worker thread for downloading models"""

    progress_updated = pyqtSignal(int)  # Progress percentage
    status_updated = pyqtSignal(str)    # Status message
    download_finished = pyqtSignal(bool, str)  # Success, message

    def __init__(self, download_type: str, models: Optional[List[str]] = None):
        super().__init__()
        self.download_type = download_type
        self.models = models or []
        self.process = None
        self.cancelled = False

    def run(self):
        """Run the download process"""
        try:
            # Prepare command
            cmd = [sys.executable, "download_models.py"]

            if self.download_type == "essential":
                cmd.append("--essential")
            elif self.download_type == "all":
                pass  # Download all by default
            elif self.download_type == "custom" and self.models:
                cmd.extend(["--models"] + self.models)

            self.status_updated.emit(f"Starting {self.download_type} download...")
            self.progress_updated.emit(0)

            # Start process
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Monitor progress
            self._monitor_progress()

            # Wait for completion
            return_code = self.process.wait()

            if self.cancelled:
                self.download_finished.emit(False, "Download cancelled")
            elif return_code == 0:
                self.progress_updated.emit(100)
                self.status_updated.emit("Download completed successfully!")
                self.download_finished.emit(True, "Download completed successfully!")
            else:
                stderr_output = self.process.stderr.read() if self.process.stderr else "Unknown error"
                self.download_finished.emit(False, f"Download failed: {stderr_output}")

        except Exception as e:
            logger.error(f"Download error: {e}")
            self.download_finished.emit(False, f"Download error: {e}")

    def _monitor_progress(self):
        """Monitor download progress from stdout"""
        if not self.process or not self.process.stdout:
            return

        progress = 0
        total_files = len(self.models) if self.models else 3  # Estimate
        files_completed = 0

        for line in iter(self.process.stdout.readline, ''):
            if self.cancelled:
                break

            line = line.strip()
            if line:
                # Parse progress from download output
                if "Downloading" in line:
                    self.status_updated.emit(line)
                elif "Successfully downloaded" in line:
                    files_completed += 1
                    progress = min(int((files_completed / total_files) * 90), 90)
                    self.progress_updated.emit(progress)
                    self.status_updated.emit(line)
                elif "Skipping" in line:
                    files_completed += 1
                    progress = min(int((files_completed / total_files) * 90), 90)
                    self.progress_updated.emit(progress)
                    self.status_updated.emit(line)
                elif "Error" in line or "Failed" in line:
                    self.status_updated.emit(line)

    def cancel(self):
        """Cancel the download"""
        self.cancelled = True
        if self.process:
            self.process.terminate()

class DownloadManager(QObject):
    """Manager for handling model downloads with GUI integration"""

    download_started = pyqtSignal(str)
    download_progress = pyqtSignal(int)
    download_status = pyqtSignal(str)
    download_finished = pyqtSignal(bool, str)

    def __init__(self):
        super().__init__()
        self.current_worker = None
        self.download_queue = []
        self.is_downloading = False

        # Model file mappings
        self.model_mappings = {
            # Essential models
            "real_esrgan": "RealESRGAN_x4plus.pth",
            "real_esrgan_x4": "RealESRGAN_x4plus.pth",
            "real_esrgan_anime": "RealESRGAN_x4plus_anime_6B.pth",

            # Waifu2x models
            "waifu2x": "waifu2x_art.pth",
            "waifu2x_cunet": "waifu2x_cunet.pth",
            "waifu2x_upconv": "waifu2x_upconv.pth",
            "waifu2x_anime_style": "waifu2x_anime.pth",

            # Traditional models
            "srcnn": "srcnn_x2.pth",
            "vdsr": "vdsr_x2.pth",
            "edsr": "edsr_x4.pth",
            "mdsr": "mdsr_x4.pth",

            # ESRGAN variants
            "esrgan": "esrgan_x4.pth",

            # Add more mappings as needed
        }

    def download_essential_models(self):
        """Download essential models (Real-ESRGAN)"""
        self._start_download("essential")

    def download_anime_models(self):
        """Download anime-focused models"""
        anime_models = ["RealESRGAN_x4plus_anime_6B.pth", "waifu2x_art.pth"]
        self._start_download("custom", anime_models)

    def download_all_models(self):
        """Download all available models"""
        self._start_download("all")

    def download_specific_models(self, model_names: List[str]):
        """Download specific models by name"""
        # Convert model names to file names
        model_files = []
        for model_name in model_names:
            if model_name in self.model_mappings:
                model_files.append(self.model_mappings[model_name])
            else:
                logger.warning(f"Unknown model: {model_name}")

        if model_files:
            self._start_download("custom", model_files)
        else:
            self.download_finished.emit(False, "No valid models to download")

    def download_category_models(self, category: str):
        """Download all models in a specific category"""
        category_mappings = {
            "essential": ["RealESRGAN_x4plus.pth", "RealESRGAN_x4plus_anime_6B.pth"],
            "traditional": ["srcnn_x2.pth", "vdsr_x2.pth", "edsr_x4.pth", "mdsr_x4.pth"],
            "esrgan": ["esrgan_x4.pth", "RealESRGAN_x4plus.pth", "RealESRGAN_x4plus_anime_6B.pth"],
            "waifu2x": ["waifu2x_art.pth", "waifu2x_cunet.pth", "waifu2x_upconv.pth"],
            "anime": ["RealESRGAN_x4plus_anime_6B.pth", "waifu2x_art.pth", "waifu2x_anime.pth"],
        }

        if category.lower() in category_mappings:
            model_files = category_mappings[category.lower()]
            self._start_download("custom", model_files)
        else:
            self.download_finished.emit(False, f"Unknown category: {category}")

    def _start_download(self, download_type: str, models: Optional[List[str]] = None):
        """Start a download process"""
        if self.is_downloading:
            self.download_queue.append((download_type, models))
            return

        self.is_downloading = True
        self.download_started.emit(download_type)

        # Create and start worker thread
        self.current_worker = DownloadWorker(download_type, models)
        self.current_worker.progress_updated.connect(self.download_progress.emit)
        self.current_worker.status_updated.connect(self.download_status.emit)
        self.current_worker.download_finished.connect(self._on_download_finished)
        self.current_worker.start()

    def _on_download_finished(self, success: bool, message: str):
        """Handle download completion"""
        self.is_downloading = False
        self.download_finished.emit(success, message)

        # Clean up worker
        if self.current_worker:
            self.current_worker.deleteLater()
            self.current_worker = None

        # Process queue
        if self.download_queue:
            download_type, models = self.download_queue.pop(0)
            self._start_download(download_type, models)

    def cancel_download(self):
        """Cancel current download"""
        if self.current_worker:
            self.current_worker.cancel()

    def is_model_available(self, model_name: str) -> bool:
        """Check if a model is available locally"""
        if model_name in self.model_mappings:
            model_file = self.model_mappings[model_name]
            model_path = Path("models") / model_file
            return model_path.exists()
        return False

    def get_available_models(self) -> List[str]:
        """Get list of locally available models"""
        available = []
        for model_name, model_file in self.model_mappings.items():
            model_path = Path("models") / model_file
            if model_path.exists():
                available.append(model_name)
        return available

    def get_model_info(self, model_name: str) -> Optional[Dict]:
        """Get information about a model"""
        if model_name not in self.model_mappings:
            return None

        model_file = self.model_mappings[model_name]
        model_path = Path("models") / model_file

        info = {
            "name": model_name,
            "file": model_file,
            "path": str(model_path),
            "available": model_path.exists(),
            "size": model_path.stat().st_size if model_path.exists() else 0
        }

        return info

# Global download manager instance
download_manager = DownloadManager()
