"""
Logging utilities for AI Video Player
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime

def setup_logging(
    log_level: int = logging.INFO,
    log_file: Optional[Path] = None,
    console_output: bool = True,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
):
    """
    Setup comprehensive logging for the application
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        console_output: Whether to output to console
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
    """
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        # Ensure log directory exists
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Log startup message
    logging.info(f"Logging initialized - Level: {logging.getLevelName(log_level)}")
    if log_file:
        logging.info(f"Log file: {log_file}")

class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(f"performance.{name}")
        self.start_time = None
    
    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.start_time = datetime.now()
        self.logger.debug(f"Started: {operation}")
    
    def end_timer(self, operation: str):
        """End timing and log duration"""
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            self.logger.info(f"Completed: {operation} in {duration:.3f}s")
            self.start_time = None
            return duration
        return None
    
    def log_metric(self, metric_name: str, value: float, unit: str = ""):
        """Log a performance metric"""
        self.logger.info(f"Metric: {metric_name} = {value:.3f} {unit}")

class ModelLogger:
    """Specialized logger for AI model operations"""
    
    def __init__(self, model_name: str):
        self.logger = logging.getLogger(f"model.{model_name}")
        self.model_name = model_name
    
    def log_load(self, success: bool, load_time: float, memory_usage: float):
        """Log model loading"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"Load {status}: {self.model_name} - Time: {load_time:.3f}s, Memory: {memory_usage:.1f}MB")
    
    def log_inference(self, input_shape: tuple, output_shape: tuple, inference_time: float):
        """Log model inference"""
        self.logger.debug(f"Inference: {input_shape} -> {output_shape} in {inference_time:.3f}s")
    
    def log_error(self, operation: str, error: Exception):
        """Log model error"""
        self.logger.error(f"Error in {operation}: {error}", exc_info=True)
