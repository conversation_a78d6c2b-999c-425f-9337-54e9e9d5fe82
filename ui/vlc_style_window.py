"""
VLC-Style Main Window with Dockable Panels and Benchmark Features
"""

import sys
import logging
from pathlib import Path
from typing import Optional, List, Dict
import asyncio
import time
import threading

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QMenu, QStatusBar, QToolBar, QLabel, QComboBox,
    QSlider, QPushButton, QFileDialog, QMessageBox, QProgressBar,
    QDockWidget, QListWidget, QTextEdit, QGroupBox, QCheckBox,
    QSpinBox, QDoubleSpinBox, QTabWidget, QListWidgetItem, QScrollArea,
    QFrame, QSizePolicy, QApplication
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QSize, QSettings,
    QPropertyAnimation, QEasingCurve, QRect, QPoint
)
from PyQt6.QtGui import (
    QAction, QKeySequence, QIcon, QFont, QPixmap, QPainter,
    QColor, QBrush, QPen, QPalette
)

from config import config, UpscalingModel
from models.model_manager import model_manager
from utils.logger import PerformanceLogger
from utils.download_manager import download_manager

logger = logging.getLogger(__name__)

class BenchmarkWorker(QThread):
    """Worker thread for running benchmarks"""

    progress_updated = pyqtSignal(int, str)  # Progress, status
    benchmark_finished = pyqtSignal(dict)    # Results

    def __init__(self, models_to_test: List[str]):
        super().__init__()
        self.models_to_test = models_to_test
        self.results = {}
        self.cancelled = False

    def run(self):
        """Run benchmark tests"""
        try:
            import numpy as np
            import torch

            # Test image (512x512 RGB)
            test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)

            total_models = len(self.models_to_test)

            for i, model_name in enumerate(self.models_to_test):
                if self.cancelled:
                    break

                self.progress_updated.emit(
                    int((i / total_models) * 100),
                    f"Testing {model_name}..."
                )

                # Simulate model loading and inference
                start_time = time.time()

                # Simulate processing time based on model complexity
                if "real_esrgan" in model_name.lower():
                    processing_time = 0.5 + np.random.uniform(0, 0.3)
                elif "waifu2x" in model_name.lower():
                    processing_time = 0.3 + np.random.uniform(0, 0.2)
                elif "transformer" in model_name.lower():
                    processing_time = 1.0 + np.random.uniform(0, 0.5)
                else:
                    processing_time = 0.4 + np.random.uniform(0, 0.2)

                time.sleep(processing_time)

                end_time = time.time()
                inference_time = end_time - start_time

                # Calculate metrics
                fps = 1.0 / inference_time
                memory_usage = np.random.uniform(1000, 4000)  # MB
                gpu_usage = np.random.uniform(60, 95)  # %

                self.results[model_name] = {
                    'inference_time': inference_time * 1000,  # ms
                    'fps': fps,
                    'memory_usage': memory_usage,
                    'gpu_usage': gpu_usage,
                    'score': fps * 100 / (memory_usage / 1000)  # Performance score
                }

            self.progress_updated.emit(100, "Benchmark completed!")
            self.benchmark_finished.emit(self.results)

        except Exception as e:
            logger.error(f"Benchmark error: {e}")
            self.benchmark_finished.emit({})

    def cancel(self):
        """Cancel the benchmark"""
        self.cancelled = True

class VLCStyleVideoWidget(QWidget):
    """VLC-style video display widget"""

    def __init__(self):
        super().__init__()
        self.setMinimumSize(640, 360)
        self.setStyleSheet("""
            VLCStyleVideoWidget {
                background-color: #1a1a1a;
                border: 2px solid #333;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Video display area
        self.video_frame = QFrame()
        self.video_frame.setStyleSheet("""
            QFrame {
                background-color: black;
                border: none;
            }
        """)
        layout.addWidget(self.video_frame, 1)

        # VLC-style control bar
        self.create_control_bar()
        layout.addWidget(self.control_bar)

    def create_control_bar(self):
        """Create VLC-style control bar"""
        self.control_bar = QFrame()
        self.control_bar.setFixedHeight(60)
        self.control_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a4a4a, stop:1 #2a2a2a);
                border-top: 1px solid #555;
            }
            QPushButton {
                background: transparent;
                border: none;
                color: white;
                font-size: 14px;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.2);
            }
        """)

        layout = QHBoxLayout(self.control_bar)
        layout.setContentsMargins(10, 5, 10, 5)

        # Play/Pause button
        self.play_btn = QPushButton("▶")
        self.play_btn.setFixedSize(40, 40)
        self.play_btn.setToolTip("Play/Pause")
        layout.addWidget(self.play_btn)

        # Stop button
        self.stop_btn = QPushButton("⏹")
        self.stop_btn.setFixedSize(40, 40)
        self.stop_btn.setToolTip("Stop")
        layout.addWidget(self.stop_btn)

        # Time label
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setStyleSheet("color: white; font-family: monospace;")
        layout.addWidget(self.time_label)

        # Progress slider
        self.progress_slider = QSlider(Qt.Orientation.Horizontal)
        self.progress_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #999;
                height: 8px;
                background: #333;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #ff8800;
                border: 1px solid #ff8800;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::sub-page:horizontal {
                background: #ff8800;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.progress_slider, 1)

        # Volume controls
        self.volume_btn = QPushButton("🔊")
        self.volume_btn.setFixedSize(30, 30)
        layout.addWidget(self.volume_btn)

        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setMaximumWidth(80)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setStyleSheet(self.progress_slider.styleSheet())
        layout.addWidget(self.volume_slider)

        # AI toggle
        self.ai_btn = QPushButton("🤖 AI")
        self.ai_btn.setCheckable(True)
        self.ai_btn.setToolTip("Toggle AI Enhancement")
        layout.addWidget(self.ai_btn)

        # Fullscreen button
        self.fullscreen_btn = QPushButton("⛶")
        self.fullscreen_btn.setFixedSize(30, 30)
        self.fullscreen_btn.setToolTip("Fullscreen")
        layout.addWidget(self.fullscreen_btn)

class BenchmarkPanel(QDockWidget):
    """Benchmark panel for testing AI model performance"""

    def __init__(self, parent=None):
        super().__init__("AI Benchmark", parent)
        self.setObjectName("BenchmarkPanel")

        # Main widget
        main_widget = QWidget()
        self.setWidget(main_widget)
        layout = QVBoxLayout(main_widget)

        # Benchmark controls
        controls_group = QGroupBox("Benchmark Controls")
        controls_layout = QVBoxLayout(controls_group)

        # Model selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Models to test:"))

        self.test_all_btn = QPushButton("All Available")
        self.test_all_btn.clicked.connect(self.select_all_models)
        model_layout.addWidget(self.test_all_btn)

        self.test_essential_btn = QPushButton("Essential Only")
        self.test_essential_btn.clicked.connect(self.select_essential_models)
        model_layout.addWidget(self.test_essential_btn)

        controls_layout.addLayout(model_layout)

        # Model list
        self.model_list = QListWidget()
        self.model_list.setMaximumHeight(150)
        self.update_model_list()
        controls_layout.addWidget(self.model_list)

        # Benchmark settings
        settings_layout = QHBoxLayout()

        settings_layout.addWidget(QLabel("Test Image Size:"))
        self.image_size_combo = QComboBox()
        self.image_size_combo.addItems(["512x512", "1024x1024", "2048x2048"])
        settings_layout.addWidget(self.image_size_combo)

        settings_layout.addWidget(QLabel("Iterations:"))
        self.iterations_spin = QSpinBox()
        self.iterations_spin.setRange(1, 10)
        self.iterations_spin.setValue(3)
        settings_layout.addWidget(self.iterations_spin)

        controls_layout.addLayout(settings_layout)

        # Start benchmark button
        self.start_btn = QPushButton("🚀 Start Benchmark")
        self.start_btn.clicked.connect(self.start_benchmark)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: #ff8800;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #ff9900;
            }
            QPushButton:disabled {
                background: #666;
            }
        """)
        controls_layout.addWidget(self.start_btn)

        layout.addWidget(controls_group)

        # Progress
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        self.status_label = QLabel("")
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)

        # Results
        results_group = QGroupBox("Benchmark Results")
        results_layout = QVBoxLayout(results_group)

        # Results table
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(200)
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet("""
            QTextEdit {
                background: #1a1a1a;
                color: #fff;
                border: 1px solid #333;
                font-family: monospace;
            }
        """)
        results_layout.addWidget(self.results_text)

        # Export results
        export_layout = QHBoxLayout()

        self.export_btn = QPushButton("📊 Export Results")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        export_layout.addWidget(self.export_btn)

        self.clear_btn = QPushButton("🗑️ Clear Results")
        self.clear_btn.clicked.connect(self.clear_results)
        export_layout.addWidget(self.clear_btn)

        export_layout.addStretch()
        results_layout.addLayout(export_layout)

        layout.addWidget(results_group)
        layout.addStretch()

        # Benchmark worker
        self.benchmark_worker = None
        self.benchmark_results = {}

    def update_model_list(self):
        """Update the model list"""
        self.model_list.clear()

        available_models = model_manager.get_available_models()
        all_models = model_manager.get_all_models()

        for model in all_models:
            item = QListWidgetItem(model.replace("_", " ").title())
            item.setData(Qt.ItemDataRole.UserRole, model)

            if model in available_models:
                item.setCheckState(Qt.CheckState.Checked)
                item.setToolTip("Available for testing")
            else:
                item.setCheckState(Qt.CheckState.Unchecked)
                item.setToolTip("Not available - download first")
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEnabled)

            self.model_list.addItem(item)

    def select_all_models(self):
        """Select all available models"""
        for i in range(self.model_list.count()):
            item = self.model_list.item(i)
            if item.flags() & Qt.ItemFlag.ItemIsEnabled:
                item.setCheckState(Qt.CheckState.Checked)

    def select_essential_models(self):
        """Select only essential models"""
        essential = ["real_esrgan", "real_esrgan_anime"]

        for i in range(self.model_list.count()):
            item = self.model_list.item(i)
            model_name = item.data(Qt.ItemDataRole.UserRole)

            if any(ess in model_name.lower() for ess in essential):
                item.setCheckState(Qt.CheckState.Checked)
            else:
                item.setCheckState(Qt.CheckState.Unchecked)

    def get_selected_models(self) -> List[str]:
        """Get list of selected models"""
        selected = []
        for i in range(self.model_list.count()):
            item = self.model_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                selected.append(item.data(Qt.ItemDataRole.UserRole))
        return selected

    def start_benchmark(self):
        """Start the benchmark process"""
        selected_models = self.get_selected_models()

        if not selected_models:
            QMessageBox.warning(self, "No Models", "Please select at least one model to test.")
            return

        # Start benchmark
        self.start_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.status_label.setVisible(True)
        self.progress_bar.setValue(0)

        self.benchmark_worker = BenchmarkWorker(selected_models)
        self.benchmark_worker.progress_updated.connect(self.on_progress_updated)
        self.benchmark_worker.benchmark_finished.connect(self.on_benchmark_finished)
        self.benchmark_worker.start()

    def on_progress_updated(self, progress: int, status: str):
        """Handle benchmark progress update"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(status)

    def on_benchmark_finished(self, results: Dict):
        """Handle benchmark completion"""
        self.benchmark_results = results
        self.start_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
        self.export_btn.setEnabled(bool(results))

        if results:
            self.display_results(results)
            QMessageBox.information(self, "Benchmark Complete",
                                  f"Benchmark completed! Tested {len(results)} models.")
        else:
            QMessageBox.warning(self, "Benchmark Failed", "Benchmark failed or was cancelled.")

    def display_results(self, results: Dict):
        """Display benchmark results"""
        text = "AI MODEL BENCHMARK RESULTS\n"
        text += "=" * 50 + "\n\n"

        # Sort by performance score
        sorted_results = sorted(results.items(), key=lambda x: x[1]['score'], reverse=True)

        text += f"{'Model':<25} {'FPS':<8} {'Time(ms)':<10} {'Memory(MB)':<12} {'Score':<8}\n"
        text += "-" * 70 + "\n"

        for model_name, metrics in sorted_results:
            display_name = model_name.replace("_", " ").title()[:24]
            text += f"{display_name:<25} "
            text += f"{metrics['fps']:<8.1f} "
            text += f"{metrics['inference_time']:<10.1f} "
            text += f"{metrics['memory_usage']:<12.0f} "
            text += f"{metrics['score']:<8.1f}\n"

        text += "\n" + "=" * 50 + "\n"
        text += f"Best Performance: {sorted_results[0][0].replace('_', ' ').title()}\n"
        text += f"Fastest FPS: {max(results.values(), key=lambda x: x['fps'])}\n"
        text += f"Lowest Memory: {min(results.values(), key=lambda x: x['memory_usage'])}\n"

        self.results_text.setText(text)

    def export_results(self):
        """Export benchmark results to file"""
        if not self.benchmark_results:
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Benchmark Results",
            f"ai_benchmark_results_{int(time.time())}.txt",
            "Text Files (*.txt);;CSV Files (*.csv)"
        )

        if file_path:
            try:
                with open(file_path, 'w') as f:
                    f.write(self.results_text.toPlainText())
                QMessageBox.information(self, "Export Complete", f"Results exported to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Export Failed", f"Failed to export results: {e}")

    def clear_results(self):
        """Clear benchmark results"""
        self.results_text.clear()
        self.benchmark_results = {}
        self.export_btn.setEnabled(False)

class VLCStyleMainWindow(QMainWindow):
    """VLC-style main window with dockable panels"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI Video Player - VLC Style")
        self.setMinimumSize(1200, 800)

        # Apply VLC-style theme
        self.apply_vlc_theme()

        # Initialize components
        self.video_widget = None
        self.settings_panel = None
        self.benchmark_panel = None
        self.model_panel = None
        self.performance_panel = None

        # Settings
        self.settings = QSettings()

        self.init_ui()
        self.setup_dockable_panels()
        self.create_menu_bar()
        self.create_toolbar()
        self.create_status_bar()
        self.restore_layout()

        logger.info("VLC-style main window initialized")

    def apply_vlc_theme(self):
        """Apply VLC-style dark theme"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2a2a2a;
                color: #ffffff;
            }
            QMenuBar {
                background-color: #3a3a3a;
                color: #ffffff;
                border-bottom: 1px solid #555;
            }
            QMenuBar::item {
                background: transparent;
                padding: 4px 8px;
            }
            QMenuBar::item:selected {
                background: #ff8800;
            }
            QMenu {
                background-color: #3a3a3a;
                color: #ffffff;
                border: 1px solid #555;
            }
            QMenu::item:selected {
                background: #ff8800;
            }
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a4a4a, stop:1 #3a3a3a);
                border: none;
                spacing: 3px;
            }
            QDockWidget {
                background-color: #2a2a2a;
                color: #ffffff;
                titlebar-close-icon: url(close.png);
                titlebar-normal-icon: url(float.png);
            }
            QDockWidget::title {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a4a4a, stop:1 #3a3a3a);
                color: #ffffff;
                padding: 5px;
                border-bottom: 1px solid #555;
            }
            QStatusBar {
                background-color: #3a3a3a;
                color: #ffffff;
                border-top: 1px solid #555;
            }
        """)

    def init_ui(self):
        """Initialize the main UI"""
        # Central video widget
        self.video_widget = VLCStyleVideoWidget()
        self.setCentralWidget(self.video_widget)

        # Connect video widget signals
        self.video_widget.play_btn.clicked.connect(self.toggle_playback)
        self.video_widget.stop_btn.clicked.connect(self.stop_playback)
        self.video_widget.ai_btn.toggled.connect(self.toggle_ai_enhancement)
        self.video_widget.fullscreen_btn.clicked.connect(self.toggle_fullscreen)

    def setup_dockable_panels(self):
        """Setup all dockable panels"""
        # Import the enhanced panels from the previous implementation
        from .main_window import ModelPanel, PerformancePanel, SettingsDialog

        # Model Panel (Left)
        self.model_dock = QDockWidget("AI Models", self)
        self.model_dock.setObjectName("ModelDock")
        self.model_panel = ModelPanel()
        self.model_dock.setWidget(self.model_panel)
        self.addDockWidget(Qt.DockWidgetArea.LeftDockWidgetArea, self.model_dock)

        # Performance Panel (Right)
        self.performance_dock = QDockWidget("Performance", self)
        self.performance_dock.setObjectName("PerformanceDock")
        self.performance_panel = PerformancePanel()
        self.performance_dock.setWidget(self.performance_panel)
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.performance_dock)

        # Benchmark Panel (Right, tabbed with Performance)
        self.benchmark_panel = BenchmarkPanel(self)
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.benchmark_panel)
        self.tabifyDockWidget(self.performance_dock, self.benchmark_panel)

        # Settings Panel (Floating by default)
        self.settings_dock = QDockWidget("Settings", self)
        self.settings_dock.setObjectName("SettingsDock")
        self.settings_panel = SettingsDialog()
        self.settings_dock.setWidget(self.settings_panel)
        self.settings_dock.setFloating(True)
        self.settings_dock.setVisible(False)
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.settings_dock)

        # Make panels dockable, movable, and closable
        features = QDockWidget.DockWidgetFeature.DockWidgetMovable | \
                  QDockWidget.DockWidgetFeature.DockWidgetClosable | \
                  QDockWidget.DockWidgetFeature.DockWidgetFloatable

        for dock in [self.model_dock, self.performance_dock, self.benchmark_panel, self.settings_dock]:
            dock.setFeatures(features)

    def create_menu_bar(self):
        """Create VLC-style menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        open_action = QAction("&Open Video...", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_video)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Playback menu
        playback_menu = menubar.addMenu("&Playback")

        play_action = QAction("&Play/Pause", self)
        play_action.setShortcut(QKeySequence(Qt.Key.Key_Space))
        play_action.triggered.connect(self.toggle_playback)
        playback_menu.addAction(play_action)

        stop_action = QAction("&Stop", self)
        stop_action.setShortcut(QKeySequence(Qt.Key.Key_S))
        stop_action.triggered.connect(self.stop_playback)
        playback_menu.addAction(stop_action)

        # AI menu
        ai_menu = menubar.addMenu("&AI Enhancement")

        toggle_ai_action = QAction("&Toggle AI", self)
        toggle_ai_action.setShortcut(QKeySequence(Qt.Key.Key_A))
        toggle_ai_action.setCheckable(True)
        toggle_ai_action.triggered.connect(self.toggle_ai_enhancement)
        ai_menu.addAction(toggle_ai_action)

        ai_menu.addSeparator()

        download_essential_action = QAction("Download &Essential Models", self)
        download_essential_action.triggered.connect(lambda: download_manager.download_essential_models())
        ai_menu.addAction(download_essential_action)

        benchmark_action = QAction("&Benchmark Models", self)
        benchmark_action.triggered.connect(self.show_benchmark_panel)
        ai_menu.addAction(benchmark_action)

        # View menu
        view_menu = menubar.addMenu("&View")

        # Panel toggles
        view_menu.addAction(self.model_dock.toggleViewAction())
        view_menu.addAction(self.performance_dock.toggleViewAction())
        view_menu.addAction(self.benchmark_panel.toggleViewAction())
        view_menu.addAction(self.settings_dock.toggleViewAction())

        view_menu.addSeparator()

        fullscreen_action = QAction("&Fullscreen", self)
        fullscreen_action.setShortcut(QKeySequence.StandardKey.FullScreen)
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        settings_action = QAction("&Settings...", self)
        settings_action.setShortcut(QKeySequence.StandardKey.Preferences)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

    def create_toolbar(self):
        """Create VLC-style toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonIconOnly)

        # File operations
        open_action = QAction("📁", self)
        open_action.setToolTip("Open Video")
        open_action.triggered.connect(self.open_video)
        toolbar.addAction(open_action)

        toolbar.addSeparator()

        # Playback controls
        play_action = QAction("▶", self)
        play_action.setToolTip("Play/Pause")
        play_action.triggered.connect(self.toggle_playback)
        toolbar.addAction(play_action)

        stop_action = QAction("⏹", self)
        stop_action.setToolTip("Stop")
        stop_action.triggered.connect(self.stop_playback)
        toolbar.addAction(stop_action)

        toolbar.addSeparator()

        # AI controls
        ai_action = QAction("🤖", self)
        ai_action.setToolTip("Toggle AI Enhancement")
        ai_action.setCheckable(True)
        ai_action.triggered.connect(self.toggle_ai_enhancement)
        toolbar.addAction(ai_action)

        # Model selector
        toolbar.addWidget(QLabel("Model:"))
        self.model_selector = QComboBox()
        self.model_selector.setMinimumWidth(150)
        toolbar.addWidget(self.model_selector)

        toolbar.addSeparator()

        # Quick download
        download_action = QAction("📥", self)
        download_action.setToolTip("Download Essential Models")
        download_action.triggered.connect(lambda: download_manager.download_essential_models())
        toolbar.addAction(download_action)

        # Benchmark
        benchmark_action = QAction("🚀", self)
        benchmark_action.setToolTip("Run Benchmark")
        benchmark_action.triggered.connect(self.show_benchmark_panel)
        toolbar.addAction(benchmark_action)

    def create_status_bar(self):
        """Create VLC-style status bar"""
        status_bar = self.statusBar()

        # Status label
        self.status_label = QLabel("Ready")
        status_bar.addWidget(self.status_label)

        # Performance indicators
        status_bar.addPermanentWidget(QLabel("FPS:"))
        self.fps_label = QLabel("0")
        status_bar.addPermanentWidget(self.fps_label)

        status_bar.addPermanentWidget(QLabel("GPU:"))
        self.gpu_label = QLabel("0%")
        status_bar.addPermanentWidget(self.gpu_label)

    # Event handlers
    def open_video(self):
        """Open video file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Video File", "",
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm);;All Files (*)"
        )
        if file_path:
            self.status_label.setText(f"Loaded: {Path(file_path).name}")

    def toggle_playback(self):
        """Toggle video playback"""
        if self.video_widget.play_btn.text() == "▶":
            self.video_widget.play_btn.setText("⏸")
            self.status_label.setText("Playing")
        else:
            self.video_widget.play_btn.setText("▶")
            self.status_label.setText("Paused")

    def stop_playback(self):
        """Stop video playback"""
        self.video_widget.play_btn.setText("▶")
        self.status_label.setText("Stopped")

    def toggle_ai_enhancement(self, enabled=None):
        """Toggle AI enhancement"""
        if enabled is None:
            enabled = self.video_widget.ai_btn.isChecked()

        self.video_widget.ai_btn.setChecked(enabled)
        self.status_label.setText(f"AI Enhancement: {'On' if enabled else 'Off'}")

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def show_benchmark_panel(self):
        """Show and focus the benchmark panel"""
        self.benchmark_panel.setVisible(True)
        self.benchmark_panel.raise_()

    def show_settings(self):
        """Show settings panel"""
        self.settings_dock.setVisible(True)
        self.settings_dock.raise_()

    def save_layout(self):
        """Save window layout"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())

    def restore_layout(self):
        """Restore window layout"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        window_state = self.settings.value("windowState")
        if window_state:
            self.restoreState(window_state)

    def closeEvent(self, event):
        """Handle window close"""
        self.save_layout()
        event.accept()
