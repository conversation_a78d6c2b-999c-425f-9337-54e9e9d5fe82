"""
Main Window for AI-Enhanced Video Player
Comprehensive UI with support for ALL AI upscaling models
"""

import sys
import logging
from pathlib import Path
from typing import Optional, List
import asyncio

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QMenu, QStatusBar, QToolBar, QLabel, QComboBox,
    QSlider, QPushButton, QFileDialog, QMessageBox, QProgressBar,
    QDockWidget, QListWidget, QTextEdit, QGroupBox, QCheckBox,
    QSpinBox, QDoubleSpinBox, QTabWidget
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QSize, QSettings,
    QPropertyAnimation, QEasingCurve, QRect
)
from PyQt6.QtGui import (
    QAction, QKeySequence, QIcon, QFont, QPixmap, QPainter,
    QColor, QBrush, QPen
)

from config import config, UpscalingModel
from models.model_manager import model_manager
# UI components will be created inline for now
# from .video_widget import VideoWidget
# from .controls_panel import ControlsPanel
# from .settings_dialog import SettingsDialog
# from .model_panel import ModelPanel
# from .performance_panel import PerformancePanel
from utils.logger import PerformanceLogger

logger = logging.getLogger(__name__)

# Placeholder UI components
class VideoWidget(QWidget):
    """Placeholder video widget"""
    frame_ready = pyqtSignal(object)
    playback_finished = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.setMinimumSize(640, 480)
        self.setStyleSheet("background-color: black;")

        layout = QVBoxLayout(self)
        label = QLabel("Video Display Area\n\nOpen a video file to start")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: white; font-size: 18px;")
        layout.addWidget(label)

    def load_video(self, path): pass
    def play(self): pass
    def pause(self): pass
    def stop(self): pass
    def seek(self, position): pass
    def enable_ai_upscaling(self, enabled): pass
    def set_ai_model(self, model): pass
    def cleanup(self): pass

class ControlsPanel(QWidget):
    """Placeholder controls panel"""
    play_pause_clicked = pyqtSignal()
    stop_clicked = pyqtSignal()
    position_changed = pyqtSignal(float)

    def __init__(self):
        super().__init__()
        layout = QHBoxLayout(self)

        self.play_btn = QPushButton("Play")
        self.stop_btn = QPushButton("Stop")
        self.position_slider = QSlider(Qt.Orientation.Horizontal)

        layout.addWidget(self.play_btn)
        layout.addWidget(self.stop_btn)
        layout.addWidget(self.position_slider, 1)

        self.play_btn.clicked.connect(self.play_pause_clicked.emit)
        self.stop_btn.clicked.connect(self.stop_clicked.emit)

class ModelPanel(QWidget):
    """Placeholder model panel"""
    model_selected = pyqtSignal(str)
    model_loaded = pyqtSignal(str)
    model_unloaded = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        layout = QVBoxLayout(self)

        # Available models list
        layout.addWidget(QLabel("Available Models:"))
        self.model_list = QListWidget()
        layout.addWidget(self.model_list)

        # Load/Unload buttons
        btn_layout = QHBoxLayout()
        self.load_btn = QPushButton("Load Model")
        self.unload_btn = QPushButton("Unload Model")
        btn_layout.addWidget(self.load_btn)
        btn_layout.addWidget(self.unload_btn)
        layout.addLayout(btn_layout)

        # Model settings
        settings_group = QGroupBox("Model Settings")
        settings_layout = QVBoxLayout(settings_group)

        # Scale factor
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("Scale Factor:"))
        self.scale_spin = QSpinBox()
        self.scale_spin.setRange(1, 8)
        self.scale_spin.setValue(4)
        scale_layout.addWidget(self.scale_spin)
        settings_layout.addLayout(scale_layout)

        # Tile size
        tile_layout = QHBoxLayout()
        tile_layout.addWidget(QLabel("Tile Size:"))
        self.tile_spin = QSpinBox()
        self.tile_spin.setRange(128, 2048)
        self.tile_spin.setValue(512)
        tile_layout.addWidget(self.tile_spin)
        settings_layout.addLayout(tile_layout)

        layout.addWidget(settings_group)

        self.update_model_list()

    def update_model_list(self):
        self.model_list.clear()
        for model in model_manager.get_all_models():
            self.model_list.addItem(model.replace("_", " ").title())

    def show_load_dialog(self): pass
    def load_model(self, name): pass

class PerformancePanel(QWidget):
    """Placeholder performance panel"""

    def __init__(self):
        super().__init__()
        layout = QVBoxLayout(self)

        # Performance metrics
        metrics_group = QGroupBox("Performance Metrics")
        metrics_layout = QVBoxLayout(metrics_group)

        self.fps_label = QLabel("FPS: 0.0")
        self.gpu_label = QLabel("GPU Usage: 0%")
        self.memory_label = QLabel("Memory: 0 MB")
        self.inference_label = QLabel("Inference Time: 0ms")

        metrics_layout.addWidget(self.fps_label)
        metrics_layout.addWidget(self.gpu_label)
        metrics_layout.addWidget(self.memory_label)
        metrics_layout.addWidget(self.inference_label)

        layout.addWidget(metrics_group)

        # Performance graph placeholder
        graph_group = QGroupBox("Performance Graph")
        graph_layout = QVBoxLayout(graph_group)
        graph_placeholder = QLabel("Performance graph will be displayed here")
        graph_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        graph_placeholder.setMinimumHeight(200)
        graph_placeholder.setStyleSheet("border: 1px solid gray;")
        graph_layout.addWidget(graph_placeholder)
        layout.addWidget(graph_group)

    def update_frame_metrics(self, data): pass
    def get_current_metrics(self):
        return {"fps": 0, "gpu_usage": 0, "memory_usage": 0}

class SettingsDialog(QWidget):
    """Placeholder settings dialog"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.setModal(True)

    def exec(self):
        self.show()

class MainWindow(QMainWindow):
    """Main application window with comprehensive AI video enhancement features"""

    def __init__(self):
        super().__init__()
        self.settings = QSettings()
        self.performance_logger = PerformanceLogger("ui")

        # Core components
        self.video_widget = None
        self.controls_panel = None
        self.model_panel = None
        self.performance_panel = None
        self.settings_dialog = None

        # State
        self.current_video_path = None
        self.is_playing = False
        self.current_model = None
        self.upscaling_enabled = False

        # Timers
        self.status_timer = QTimer()
        self.performance_timer = QTimer()

        self.init_ui()
        self.setup_connections()
        self.restore_settings()

        logger.info("Main window initialized")

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("AI-Enhanced Video Player - All Models Supported")
        self.setMinimumSize(1024, 768)

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Create splitter for main content
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)

        # Left panel for video and controls
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # Video widget
        self.video_widget = VideoWidget()
        left_layout.addWidget(self.video_widget, 1)

        # Controls panel
        self.controls_panel = ControlsPanel()
        left_layout.addWidget(self.controls_panel)

        main_splitter.addWidget(left_widget)

        # Right panel for AI models and settings
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Create tabs for different panels
        tab_widget = QTabWidget()

        # Model selection and configuration tab
        self.model_panel = ModelPanel()
        tab_widget.addTab(self.model_panel, "AI Models")

        # Performance monitoring tab
        self.performance_panel = PerformancePanel()
        tab_widget.addTab(self.performance_panel, "Performance")

        # Settings tab (placeholder for now)
        settings_widget = QWidget()
        tab_widget.addTab(settings_widget, "Settings")

        right_layout.addWidget(tab_widget)
        main_splitter.addWidget(right_widget)

        # Set splitter proportions (70% video, 30% controls)
        main_splitter.setSizes([700, 300])

        # Create menu bar
        self.create_menu_bar()

        # Create toolbar
        self.create_toolbar()

        # Create status bar
        self.create_status_bar()

        # Create dock widgets
        self.create_dock_widgets()

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        open_action = QAction("&Open Video...", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.setStatusTip("Open a video file")
        open_action.triggered.connect(self.open_video)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip("Exit the application")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # AI menu
        ai_menu = menubar.addMenu("&AI Models")

        load_model_action = QAction("&Load Model...", self)
        load_model_action.setStatusTip("Load an AI upscaling model")
        load_model_action.triggered.connect(self.load_ai_model)
        ai_menu.addAction(load_model_action)

        unload_models_action = QAction("&Unload All Models", self)
        unload_models_action.setStatusTip("Unload all loaded AI models")
        unload_models_action.triggered.connect(self.unload_all_models)
        ai_menu.addAction(unload_models_action)

        ai_menu.addSeparator()

        # Add submenu for each model category
        traditional_menu = ai_menu.addMenu("Traditional SR")
        esrgan_menu = ai_menu.addMenu("ESRGAN Family")
        waifu2x_menu = ai_menu.addMenu("Waifu2x Variants")
        transformer_menu = ai_menu.addMenu("Transformer Models")
        video_menu = ai_menu.addMenu("Video Models")
        realtime_menu = ai_menu.addMenu("Real-time Models")

        # Populate model menus
        self.populate_model_menus({
            "Traditional SR": traditional_menu,
            "ESRGAN Family": esrgan_menu,
            "Waifu2x Variants": waifu2x_menu,
            "Transformer Models": transformer_menu,
            "Video Models": video_menu,
            "Real-time Models": realtime_menu
        })

        # View menu
        view_menu = menubar.addMenu("&View")

        fullscreen_action = QAction("&Fullscreen", self)
        fullscreen_action.setShortcut(QKeySequence.StandardKey.FullScreen)
        fullscreen_action.setCheckable(True)
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        settings_action = QAction("&Settings...", self)
        settings_action.setShortcut(QKeySequence.StandardKey.Preferences)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def populate_model_menus(self, menus: dict):
        """Populate model category menus"""
        model_categories = {
            "Traditional SR": ["srcnn", "vdsr", "edsr", "mdsr"],
            "ESRGAN Family": ["esrgan", "real_esrgan", "real_esrgan_x4", "real_esrgan_anime"],
            "Waifu2x Variants": ["waifu2x", "waifu2x_cunet", "waifu2x_upconv", "waifu2x_anime_style"],
            "Transformer Models": ["swinir", "hat", "swin2sr"],
            "Video Models": ["basicvsr", "basicvsr_plus", "iconvsr", "tdan", "edvr"],
            "Real-time Models": ["realtime_esrgan", "fast_srgan", "efficient_sr"]
        }

        for category, models in model_categories.items():
            if category in menus:
                menu = menus[category]
                for model_name in models:
                    action = QAction(model_name.replace("_", " ").title(), self)
                    action.setData(model_name)
                    action.triggered.connect(lambda checked, m=model_name: self.load_specific_model(m))
                    menu.addAction(action)

    def create_toolbar(self):
        """Create application toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)

        # Open video
        open_action = QAction("Open", self)
        open_action.setStatusTip("Open video file")
        open_action.triggered.connect(self.open_video)
        toolbar.addAction(open_action)

        toolbar.addSeparator()

        # Play/Pause
        self.play_action = QAction("Play", self)
        self.play_action.setStatusTip("Play/Pause video")
        self.play_action.triggered.connect(self.toggle_playback)
        toolbar.addAction(self.play_action)

        # Stop
        stop_action = QAction("Stop", self)
        stop_action.setStatusTip("Stop video")
        stop_action.triggered.connect(self.stop_video)
        toolbar.addAction(stop_action)

        toolbar.addSeparator()

        # AI Toggle
        self.ai_toggle_action = QAction("AI Off", self)
        self.ai_toggle_action.setCheckable(True)
        self.ai_toggle_action.setStatusTip("Toggle AI upscaling")
        self.ai_toggle_action.triggered.connect(self.toggle_ai_upscaling)
        toolbar.addAction(self.ai_toggle_action)

        # Model selector
        toolbar.addWidget(QLabel("Model:"))
        self.model_selector = QComboBox()
        self.model_selector.setMinimumWidth(150)
        self.model_selector.currentTextChanged.connect(self.on_model_changed)
        toolbar.addWidget(self.model_selector)

        # Update model list
        self.update_model_selector()

    def create_status_bar(self):
        """Create status bar with performance indicators"""
        status_bar = self.statusBar()

        # Main status label
        self.status_label = QLabel("Ready")
        status_bar.addWidget(self.status_label)

        # Performance indicators
        status_bar.addPermanentWidget(QLabel("FPS:"))
        self.fps_label = QLabel("0")
        status_bar.addPermanentWidget(self.fps_label)

        status_bar.addPermanentWidget(QLabel("GPU:"))
        self.gpu_label = QLabel("0%")
        status_bar.addPermanentWidget(self.gpu_label)

        status_bar.addPermanentWidget(QLabel("Memory:"))
        self.memory_label = QLabel("0 MB")
        status_bar.addPermanentWidget(self.memory_label)

        # Progress bar for operations
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_bar.addPermanentWidget(self.progress_bar)

    def create_dock_widgets(self):
        """Create dockable widgets"""
        # Model information dock
        model_info_dock = QDockWidget("Model Information", self)
        model_info_widget = QTextEdit()
        model_info_widget.setMaximumHeight(150)
        model_info_widget.setReadOnly(True)
        model_info_dock.setWidget(model_info_widget)
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, model_info_dock)

        # Log dock
        log_dock = QDockWidget("Logs", self)
        self.log_widget = QTextEdit()
        self.log_widget.setMaximumHeight(150)
        self.log_widget.setReadOnly(True)
        log_dock.setWidget(self.log_widget)
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, log_dock)

        # Tabify docks
        self.tabifyDockWidget(model_info_dock, log_dock)

    def setup_connections(self):
        """Setup signal connections"""
        # Video widget connections
        if self.video_widget:
            self.video_widget.frame_ready.connect(self.on_frame_ready)
            self.video_widget.playback_finished.connect(self.on_playback_finished)

        # Controls panel connections
        if self.controls_panel:
            self.controls_panel.play_pause_clicked.connect(self.toggle_playback)
            self.controls_panel.stop_clicked.connect(self.stop_video)
            self.controls_panel.position_changed.connect(self.seek_video)

        # Model panel connections
        if self.model_panel:
            self.model_panel.model_selected.connect(self.on_model_selected)
            self.model_panel.model_loaded.connect(self.on_model_loaded)
            self.model_panel.model_unloaded.connect(self.on_model_unloaded)

        # Timers
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second

        self.performance_timer.timeout.connect(self.update_performance)
        self.performance_timer.start(500)  # Update every 500ms

    def update_model_selector(self):
        """Update the model selector combo box"""
        self.model_selector.clear()
        self.model_selector.addItem("None")

        available_models = model_manager.get_available_models()
        for model_name in available_models:
            display_name = model_name.replace("_", " ").title()
            self.model_selector.addItem(display_name, model_name)

    def open_video(self):
        """Open video file dialog"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Video File",
            "",
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v *.3gp);;All Files (*)"
        )

        if file_path:
            self.load_video(file_path)

    def load_video(self, file_path: str):
        """Load a video file"""
        try:
            self.current_video_path = file_path

            if self.video_widget:
                self.video_widget.load_video(file_path)

            self.status_label.setText(f"Loaded: {Path(file_path).name}")
            logger.info(f"Loaded video: {file_path}")

        except Exception as e:
            logger.error(f"Error loading video: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load video:\n{e}")

    def toggle_playback(self):
        """Toggle video playback"""
        if self.video_widget and self.current_video_path:
            if self.is_playing:
                self.video_widget.pause()
                self.play_action.setText("Play")
                self.is_playing = False
            else:
                self.video_widget.play()
                self.play_action.setText("Pause")
                self.is_playing = True

    def stop_video(self):
        """Stop video playback"""
        if self.video_widget:
            self.video_widget.stop()
            self.play_action.setText("Play")
            self.is_playing = False

    def seek_video(self, position: float):
        """Seek to position in video (0.0 to 1.0)"""
        if self.video_widget:
            self.video_widget.seek(position)

    def toggle_ai_upscaling(self):
        """Toggle AI upscaling on/off"""
        self.upscaling_enabled = self.ai_toggle_action.isChecked()

        if self.upscaling_enabled:
            self.ai_toggle_action.setText("AI On")
            if self.video_widget:
                self.video_widget.enable_ai_upscaling(True)
        else:
            self.ai_toggle_action.setText("AI Off")
            if self.video_widget:
                self.video_widget.enable_ai_upscaling(False)

        logger.info(f"AI upscaling {'enabled' if self.upscaling_enabled else 'disabled'}")

    def load_ai_model(self):
        """Show model loading dialog"""
        if self.model_panel:
            self.model_panel.show_load_dialog()

    def load_specific_model(self, model_name: str):
        """Load a specific AI model"""
        if self.model_panel:
            self.model_panel.load_model(model_name)

    def unload_all_models(self):
        """Unload all AI models"""
        model_manager.unload_all_models()
        self.update_model_selector()
        self.status_label.setText("All models unloaded")
        logger.info("All AI models unloaded")

    def on_model_changed(self, model_name: str):
        """Handle model selection change"""
        if model_name == "None":
            self.current_model = None
        else:
            # Get actual model name from combo box data
            index = self.model_selector.currentIndex()
            if index > 0:  # Skip "None" option
                self.current_model = self.model_selector.itemData(index)

        if self.video_widget:
            self.video_widget.set_ai_model(self.current_model)

    def on_model_selected(self, model_name: str):
        """Handle model selection from model panel"""
        # Update combo box
        for i in range(self.model_selector.count()):
            if self.model_selector.itemData(i) == model_name:
                self.model_selector.setCurrentIndex(i)
                break

    def on_model_loaded(self, model_name: str):
        """Handle model loaded event"""
        self.update_model_selector()
        self.status_label.setText(f"Loaded model: {model_name}")

    def on_model_unloaded(self, model_name: str):
        """Handle model unloaded event"""
        self.update_model_selector()
        self.status_label.setText(f"Unloaded model: {model_name}")

    def on_frame_ready(self, frame_data):
        """Handle new frame from video widget"""
        # Update performance metrics
        if self.performance_panel:
            self.performance_panel.update_frame_metrics(frame_data)

    def on_playback_finished(self):
        """Handle playback finished"""
        self.is_playing = False
        self.play_action.setText("Play")
        self.status_label.setText("Playback finished")

    def update_status(self):
        """Update status bar information"""
        if self.video_widget:
            # Update playback info
            pass

    def update_performance(self):
        """Update performance indicators"""
        if self.performance_panel:
            metrics = self.performance_panel.get_current_metrics()

            self.fps_label.setText(f"{metrics.get('fps', 0):.1f}")
            self.gpu_label.setText(f"{metrics.get('gpu_usage', 0):.1f}%")
            self.memory_label.setText(f"{metrics.get('memory_usage', 0):.0f} MB")

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def show_settings(self):
        """Show settings dialog"""
        if not self.settings_dialog:
            self.settings_dialog = SettingsDialog(self)

        self.settings_dialog.exec()

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About AI Video Player",
            "AI-Enhanced Video Player\n\n"
            "Supports ALL available AI upscaling models:\n"
            "• Traditional SR (SRCNN, VDSR, EDSR, MDSR)\n"
            "• ESRGAN Family (ESRGAN, Real-ESRGAN variants)\n"
            "• Waifu2x Variants (CUNet, UpConv, Anime)\n"
            "• Transformer Models (SwinIR, HAT, Swin2SR)\n"
            "• Video Models (BasicVSR, IconVSR, TDAN, EDVR)\n"
            "• Real-time Models (Fast-SRGAN, Efficient-SR)\n"
            "• Diffusion Models (LDSR, Stable Diffusion)\n\n"
            "With ROCm acceleration for AMD GPUs"
        )

    def save_settings(self):
        """Save window settings"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
        self.settings.setValue("current_model", self.current_model)
        self.settings.setValue("ai_enabled", self.upscaling_enabled)

    def restore_settings(self):
        """Restore window settings"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        window_state = self.settings.value("windowState")
        if window_state:
            self.restoreState(window_state)

        # Restore model selection
        saved_model = self.settings.value("current_model")
        if saved_model:
            for i in range(self.model_selector.count()):
                if self.model_selector.itemData(i) == saved_model:
                    self.model_selector.setCurrentIndex(i)
                    break

        # Restore AI state
        ai_enabled = self.settings.value("ai_enabled", False, type=bool)
        self.ai_toggle_action.setChecked(ai_enabled)
        self.toggle_ai_upscaling()

    def closeEvent(self, event):
        """Handle window close event"""
        self.save_settings()

        # Cleanup
        if self.video_widget:
            self.video_widget.cleanup()

        model_manager.unload_all_models()

        event.accept()
