"""
Main Window for AI-Enhanced Video Player
Comprehensive UI with support for ALL AI upscaling models
"""

import sys
import logging
from pathlib import Path
from typing import Optional, List
import asyncio

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QMenu, QStatusBar, QToolBar, QLabel, QComboBox,
    QSlider, QPushButton, QFileDialog, QMessageBox, QProgressBar,
    QDockWidget, QListWidget, QTextEdit, QGroupBox, QCheckBox,
    QSpinBox, QDoubleSpinBox, QTabWidget, QListWidgetItem, QScrollArea
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QSize, QSettings,
    QPropertyAnimation, QEasingCurve, QRect
)
from PyQt6.QtGui import (
    QAction, QKeySequence, QIcon, QFont, QPixmap, QPainter,
    QColor, QBrush, QPen
)

from config import config, UpscalingModel
from models.model_manager import model_manager
# UI components will be created inline for now
# from .video_widget import VideoWidget
# from .controls_panel import ControlsPanel
# from .settings_dialog import SettingsDialog
# from .model_panel import ModelPanel
# from .performance_panel import PerformancePanel
from utils.logger import PerformanceLogger
from utils.download_manager import download_manager

logger = logging.getLogger(__name__)

# Placeholder UI components
class VideoWidget(QWidget):
    """Placeholder video widget"""
    frame_ready = pyqtSignal(object)
    playback_finished = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.setMinimumSize(640, 480)
        self.setStyleSheet("background-color: black;")

        layout = QVBoxLayout(self)
        label = QLabel("Video Display Area\n\nOpen a video file to start")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: white; font-size: 18px;")
        layout.addWidget(label)

    def load_video(self, path): pass
    def play(self): pass
    def pause(self): pass
    def stop(self): pass
    def seek(self, position): pass
    def enable_ai_upscaling(self, enabled): pass
    def set_ai_model(self, model): pass
    def cleanup(self): pass

class ControlsPanel(QWidget):
    """Placeholder controls panel"""
    play_pause_clicked = pyqtSignal()
    stop_clicked = pyqtSignal()
    position_changed = pyqtSignal(float)

    def __init__(self):
        super().__init__()
        layout = QHBoxLayout(self)

        self.play_btn = QPushButton("Play")
        self.stop_btn = QPushButton("Stop")
        self.position_slider = QSlider(Qt.Orientation.Horizontal)

        layout.addWidget(self.play_btn)
        layout.addWidget(self.stop_btn)
        layout.addWidget(self.position_slider, 1)

        self.play_btn.clicked.connect(self.play_pause_clicked.emit)
        self.stop_btn.clicked.connect(self.stop_clicked.emit)

class ModelPanel(QWidget):
    """Enhanced model panel with download capabilities and comprehensive settings"""
    model_selected = pyqtSignal(str)
    model_loaded = pyqtSignal(str)
    model_unloaded = pyqtSignal(str)
    download_started = pyqtSignal(str)
    download_finished = pyqtSignal(str, bool)

    def __init__(self):
        super().__init__()
        self.current_model = None
        self.downloading_models = set()
        self.init_ui()
        self.setup_download_connections()
        self.update_model_list()

    def init_ui(self):
        layout = QVBoxLayout(self)

        # Quick Download Section
        download_group = QGroupBox("Quick Download")
        download_layout = QVBoxLayout(download_group)

        # Quick download buttons
        quick_btn_layout = QHBoxLayout()

        self.download_essential_btn = QPushButton("📥 Essential Models")
        self.download_essential_btn.setToolTip("Download Real-ESRGAN models (recommended)")
        self.download_essential_btn.clicked.connect(self.download_essential_models)

        self.download_anime_btn = QPushButton("🎌 Anime Models")
        self.download_anime_btn.setToolTip("Download Waifu2x and anime-optimized models")
        self.download_anime_btn.clicked.connect(self.download_anime_models)

        self.download_all_btn = QPushButton("📦 All Models")
        self.download_all_btn.setToolTip("Download all available models")
        self.download_all_btn.clicked.connect(self.download_all_models)

        quick_btn_layout.addWidget(self.download_essential_btn)
        quick_btn_layout.addWidget(self.download_anime_btn)
        quick_btn_layout.addWidget(self.download_all_btn)
        download_layout.addLayout(quick_btn_layout)

        # Download progress
        self.download_progress = QProgressBar()
        self.download_progress.setVisible(False)
        download_layout.addWidget(self.download_progress)

        self.download_status = QLabel("")
        self.download_status.setVisible(False)
        download_layout.addWidget(self.download_status)

        layout.addWidget(download_group)

        # Model Browser Section
        browser_group = QGroupBox("Model Browser")
        browser_layout = QVBoxLayout(browser_group)

        # Category tabs
        self.category_tabs = QTabWidget()
        self.setup_category_tabs()
        browser_layout.addWidget(self.category_tabs)

        layout.addWidget(browser_group)

        # Current Model Settings
        settings_group = QGroupBox("Current Model Settings")
        settings_layout = QVBoxLayout(settings_group)

        # Model info
        self.model_info_label = QLabel("No model selected")
        self.model_info_label.setStyleSheet("font-weight: bold; color: #666;")
        settings_layout.addWidget(self.model_info_label)

        # Settings in a scroll area for better organization
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # Basic settings
        basic_group = QGroupBox("Basic Settings")
        basic_layout = QVBoxLayout(basic_group)

        # Scale factor
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("Scale Factor:"))
        self.scale_spin = QSpinBox()
        self.scale_spin.setRange(1, 8)
        self.scale_spin.setValue(4)
        self.scale_spin.valueChanged.connect(self.on_settings_changed)
        scale_layout.addWidget(self.scale_spin)
        scale_layout.addStretch()
        basic_layout.addLayout(scale_layout)

        # Precision
        precision_layout = QHBoxLayout()
        precision_layout.addWidget(QLabel("Precision:"))
        self.precision_combo = QComboBox()
        self.precision_combo.addItems(["fp16", "fp32", "int8"])
        self.precision_combo.setCurrentText("fp16")
        self.precision_combo.currentTextChanged.connect(self.on_settings_changed)
        precision_layout.addWidget(self.precision_combo)
        precision_layout.addStretch()
        basic_layout.addLayout(precision_layout)

        scroll_layout.addWidget(basic_group)

        # Performance settings
        perf_group = QGroupBox("Performance Settings")
        perf_layout = QVBoxLayout(perf_group)

        # Tile size
        tile_layout = QHBoxLayout()
        tile_layout.addWidget(QLabel("Tile Size:"))
        self.tile_spin = QSpinBox()
        self.tile_spin.setRange(128, 2048)
        self.tile_spin.setValue(512)
        self.tile_spin.setSingleStep(64)
        self.tile_spin.valueChanged.connect(self.on_settings_changed)
        tile_layout.addWidget(self.tile_spin)
        tile_layout.addStretch()
        perf_layout.addLayout(tile_layout)

        # Tile overlap
        overlap_layout = QHBoxLayout()
        overlap_layout.addWidget(QLabel("Tile Overlap:"))
        self.overlap_spin = QSpinBox()
        self.overlap_spin.setRange(0, 128)
        self.overlap_spin.setValue(32)
        self.overlap_spin.valueChanged.connect(self.on_settings_changed)
        overlap_layout.addWidget(self.overlap_spin)
        overlap_layout.addStretch()
        perf_layout.addLayout(overlap_layout)

        # Batch size
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("Batch Size:"))
        self.batch_spin = QSpinBox()
        self.batch_spin.setRange(1, 16)
        self.batch_spin.setValue(1)
        self.batch_spin.valueChanged.connect(self.on_settings_changed)
        batch_layout.addWidget(self.batch_spin)
        batch_layout.addStretch()
        perf_layout.addLayout(batch_layout)

        scroll_layout.addWidget(perf_group)

        # Advanced settings
        advanced_group = QGroupBox("Advanced Settings")
        advanced_layout = QVBoxLayout(advanced_group)

        # Denoise strength
        denoise_layout = QHBoxLayout()
        denoise_layout.addWidget(QLabel("Denoise Strength:"))
        self.denoise_slider = QSlider(Qt.Orientation.Horizontal)
        self.denoise_slider.setRange(0, 100)
        self.denoise_slider.setValue(50)
        self.denoise_slider.valueChanged.connect(self.on_settings_changed)
        self.denoise_value_label = QLabel("0.5")
        denoise_layout.addWidget(self.denoise_slider)
        denoise_layout.addWidget(self.denoise_value_label)
        advanced_layout.addLayout(denoise_layout)

        # Face enhancement
        self.face_enhance_check = QCheckBox("Face Enhancement")
        self.face_enhance_check.stateChanged.connect(self.on_settings_changed)
        advanced_layout.addWidget(self.face_enhance_check)

        # Anime mode
        self.anime_mode_check = QCheckBox("Anime Mode")
        self.anime_mode_check.stateChanged.connect(self.on_settings_changed)
        advanced_layout.addWidget(self.anime_mode_check)

        # GPU usage
        self.use_gpu_check = QCheckBox("Use GPU")
        self.use_gpu_check.setChecked(True)
        self.use_gpu_check.stateChanged.connect(self.on_settings_changed)
        advanced_layout.addWidget(self.use_gpu_check)

        scroll_layout.addWidget(advanced_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(300)
        settings_layout.addWidget(scroll_area)

        layout.addWidget(settings_group)

        # Connect denoise slider to label update
        self.denoise_slider.valueChanged.connect(
            lambda v: self.denoise_value_label.setText(f"{v/100:.1f}")
        )

    def setup_download_connections(self):
        """Setup connections to download manager"""
        download_manager.download_started.connect(self.on_download_started)
        download_manager.download_progress.connect(self.on_download_progress)
        download_manager.download_status.connect(self.on_download_status)
        download_manager.download_finished.connect(self.on_download_finished)

    def setup_category_tabs(self):
        """Setup model category tabs with download buttons"""
        categories = {
            "Essential": ["real_esrgan", "real_esrgan_x4", "real_esrgan_anime"],
            "Traditional": ["srcnn", "vdsr", "edsr", "mdsr"],
            "ESRGAN": ["esrgan", "real_esrgan", "real_esrgan_x4", "real_esrgan_anime"],
            "Waifu2x": ["waifu2x", "waifu2x_cunet", "waifu2x_upconv", "waifu2x_anime_style"],
            "Attention": ["rcan", "san", "han"],
            "Advanced": ["srgan", "fsrcnn", "lapsrn", "drcn", "drrn"],
            "Transformer": ["swinir", "hat", "swin2sr"],
            "Video": ["basicvsr", "basicvsr_plus", "iconvsr", "tdan", "edvr"],
            "Real-time": ["realtime_esrgan", "fast_srgan", "efficient_sr"]
        }

        for category, models in categories.items():
            tab_widget = QWidget()
            tab_layout = QVBoxLayout(tab_widget)

            # Category download button
            category_btn = QPushButton(f"📥 Download All {category}")
            category_btn.clicked.connect(lambda checked, m=models: self.download_models(m))
            tab_layout.addWidget(category_btn)

            # Model list for this category
            model_list = QListWidget()
            for model in models:
                item = QListWidgetItem()
                item_widget = self.create_model_item_widget(model)
                item.setSizeHint(item_widget.sizeHint())
                model_list.addItem(item)
                model_list.setItemWidget(item, item_widget)

            tab_layout.addWidget(model_list)
            self.category_tabs.addTab(tab_widget, category)

    def create_model_item_widget(self, model_name):
        """Create a widget for each model item with download/load buttons"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)

        # Model name and status
        name_label = QLabel(model_name.replace("_", " ").title())
        name_label.setMinimumWidth(150)
        layout.addWidget(name_label)

        # Status indicator
        status_label = QLabel("●")
        if model_name in model_manager.get_available_models():
            status_label.setText("● Available")
            status_label.setStyleSheet("color: green;")
        elif model_name in self.downloading_models:
            status_label.setText("● Downloading...")
            status_label.setStyleSheet("color: orange;")
        else:
            status_label.setText("● Not Downloaded")
            status_label.setStyleSheet("color: red;")

        layout.addWidget(status_label)
        layout.addStretch()

        # Download button
        download_btn = QPushButton("📥")
        download_btn.setMaximumWidth(30)
        download_btn.setToolTip("Download this model")
        download_btn.clicked.connect(lambda: self.download_models([model_name]))
        layout.addWidget(download_btn)

        # Load button
        load_btn = QPushButton("▶")
        load_btn.setMaximumWidth(30)
        load_btn.setToolTip("Load this model")
        load_btn.clicked.connect(lambda: self.load_model(model_name))
        load_btn.setEnabled(model_name in model_manager.get_available_models())
        layout.addWidget(load_btn)

        return widget

    def download_essential_models(self):
        """Download essential models"""
        download_manager.download_essential_models()

    def download_anime_models(self):
        """Download anime-focused models"""
        download_manager.download_anime_models()

    def download_all_models(self):
        """Download all available models"""
        download_manager.download_all_models()

    def download_models(self, model_list):
        """Download specific models"""
        download_manager.download_specific_models(model_list)

    def on_download_started(self, download_type):
        """Handle download started"""
        self.download_progress.setVisible(True)
        self.download_status.setVisible(True)
        self.download_status.setText(f"Starting {download_type} download...")
        self.download_progress.setValue(0)

        # Disable download buttons during download
        self.download_essential_btn.setEnabled(False)
        self.download_anime_btn.setEnabled(False)
        self.download_all_btn.setEnabled(False)

    def on_download_progress(self, progress):
        """Handle download progress update"""
        self.download_progress.setValue(progress)

    def on_download_status(self, status):
        """Handle download status update"""
        self.download_status.setText(status)

    def on_download_finished(self, success, message):
        """Handle download completion"""
        if success:
            self.download_progress.setValue(100)
            self.download_status.setText("Download completed successfully!")
        else:
            self.download_status.setText(f"Download failed: {message}")

        # Re-enable buttons
        self.download_essential_btn.setEnabled(True)
        self.download_anime_btn.setEnabled(True)
        self.download_all_btn.setEnabled(True)

        # Hide progress after a delay
        QTimer.singleShot(3000, self.hide_download_progress)

        # Update model list
        self.update_model_list()

        # Show completion message
        if success:
            QMessageBox.information(self, "Download Complete", message)
        else:
            QMessageBox.warning(self, "Download Failed", message)

    def hide_download_progress(self):
        """Hide download progress indicators"""
        self.download_progress.setVisible(False)
        self.download_status.setVisible(False)

    def update_model_list(self):
        """Update all model lists"""
        # Refresh category tabs
        for i in range(self.category_tabs.count()):
            tab_widget = self.category_tabs.widget(i)
            if tab_widget:
                model_list = tab_widget.findChild(QListWidget)
                if model_list:
                    for j in range(model_list.count()):
                        item = model_list.item(j)
                        if item:
                            widget = model_list.itemWidget(item)
                            # Update status indicators here
                            pass

    def load_model(self, model_name):
        """Load a specific model"""
        self.current_model = model_name
        self.model_info_label.setText(f"Loading: {model_name.replace('_', ' ').title()}")
        self.load_model_settings(model_name)
        self.model_selected.emit(model_name)

        # Simulate loading
        QTimer.singleShot(1000, lambda: self.on_model_loaded(model_name))

    def on_model_loaded(self, model_name):
        """Handle model loaded event"""
        self.model_info_label.setText(f"Loaded: {model_name.replace('_', ' ').title()}")
        self.model_loaded.emit(model_name)

    def load_model_settings(self, model_name):
        """Load settings for the specified model"""
        if model_name in config.models:
            model_config = config.models[model_name]

            self.scale_spin.setValue(model_config.scale_factor)
            self.precision_combo.setCurrentText(model_config.precision)
            self.tile_spin.setValue(model_config.tile_size)
            self.overlap_spin.setValue(model_config.tile_overlap)
            self.batch_spin.setValue(model_config.batch_size)
            self.denoise_slider.setValue(int(model_config.denoise_strength * 100))
            self.face_enhance_check.setChecked(model_config.face_enhance)
            self.anime_mode_check.setChecked(model_config.anime_mode)
            self.use_gpu_check.setChecked(model_config.use_gpu)

    def on_settings_changed(self):
        """Handle settings changes"""
        if self.current_model and self.current_model in config.models:
            model_config = config.models[self.current_model]

            model_config.scale_factor = self.scale_spin.value()
            model_config.precision = self.precision_combo.currentText()
            model_config.tile_size = self.tile_spin.value()
            model_config.tile_overlap = self.overlap_spin.value()
            model_config.batch_size = self.batch_spin.value()
            model_config.denoise_strength = self.denoise_slider.value() / 100.0
            model_config.face_enhance = self.face_enhance_check.isChecked()
            model_config.anime_mode = self.anime_mode_check.isChecked()
            model_config.use_gpu = self.use_gpu_check.isChecked()

            # Save configuration
            config.save_config()

    def show_load_dialog(self):
        """Show model loading dialog"""
        pass

class PerformancePanel(QWidget):
    """Enhanced performance panel with real-time monitoring"""

    def __init__(self):
        super().__init__()
        self.metrics_history = {
            'fps': [],
            'gpu_usage': [],
            'memory_usage': [],
            'inference_time': []
        }
        self.init_ui()

        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_metrics)
        self.update_timer.start(1000)  # Update every second

    def init_ui(self):
        layout = QVBoxLayout(self)

        # Quick Stats Cards
        stats_layout = QHBoxLayout()

        # FPS Card
        fps_card = self.create_metric_card("FPS", "0.0", "🎬")
        self.fps_value_label = fps_card.findChild(QLabel, "value")
        stats_layout.addWidget(fps_card)

        # GPU Card
        gpu_card = self.create_metric_card("GPU Usage", "0%", "🖥️")
        self.gpu_value_label = gpu_card.findChild(QLabel, "value")
        stats_layout.addWidget(gpu_card)

        # Memory Card
        memory_card = self.create_metric_card("Memory", "0 MB", "💾")
        self.memory_value_label = memory_card.findChild(QLabel, "value")
        stats_layout.addWidget(memory_card)

        # Inference Card
        inference_card = self.create_metric_card("Inference", "0ms", "⚡")
        self.inference_value_label = inference_card.findChild(QLabel, "value")
        stats_layout.addWidget(inference_card)

        layout.addLayout(stats_layout)

        # Performance Controls
        controls_group = QGroupBox("Performance Controls")
        controls_layout = QVBoxLayout(controls_group)

        # Performance mode
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("Performance Mode:"))
        self.performance_mode = QComboBox()
        self.performance_mode.addItems(["Balanced", "Quality", "Speed", "Custom"])
        self.performance_mode.currentTextChanged.connect(self.on_performance_mode_changed)
        mode_layout.addWidget(self.performance_mode)
        mode_layout.addStretch()
        controls_layout.addLayout(mode_layout)

        # GPU settings
        gpu_layout = QHBoxLayout()
        gpu_layout.addWidget(QLabel("GPU Memory Limit:"))
        self.gpu_memory_slider = QSlider(Qt.Orientation.Horizontal)
        self.gpu_memory_slider.setRange(10, 100)
        self.gpu_memory_slider.setValue(80)
        self.gpu_memory_slider.valueChanged.connect(self.on_gpu_memory_changed)
        self.gpu_memory_label = QLabel("80%")
        gpu_layout.addWidget(self.gpu_memory_slider)
        gpu_layout.addWidget(self.gpu_memory_label)
        controls_layout.addLayout(gpu_layout)

        # Threading
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Worker Threads:"))
        self.thread_spin = QSpinBox()
        self.thread_spin.setRange(1, 16)
        self.thread_spin.setValue(4)
        self.thread_spin.valueChanged.connect(self.on_thread_count_changed)
        thread_layout.addWidget(self.thread_spin)
        thread_layout.addStretch()
        controls_layout.addLayout(thread_layout)

        layout.addWidget(controls_group)

        # Performance Graph
        graph_group = QGroupBox("Performance History")
        graph_layout = QVBoxLayout(graph_group)

        # Graph controls
        graph_controls = QHBoxLayout()
        self.graph_metric = QComboBox()
        self.graph_metric.addItems(["FPS", "GPU Usage", "Memory", "Inference Time"])
        self.graph_metric.currentTextChanged.connect(self.update_graph)
        graph_controls.addWidget(QLabel("Show:"))
        graph_controls.addWidget(self.graph_metric)
        graph_controls.addStretch()

        self.clear_history_btn = QPushButton("Clear History")
        self.clear_history_btn.clicked.connect(self.clear_history)
        graph_controls.addWidget(self.clear_history_btn)

        graph_layout.addLayout(graph_controls)

        # Simple graph placeholder (would use matplotlib or similar in real implementation)
        self.graph_widget = QLabel("Performance graph will be displayed here")
        self.graph_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.graph_widget.setMinimumHeight(150)
        self.graph_widget.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        graph_layout.addWidget(self.graph_widget)

        layout.addWidget(graph_group)

        # System Info
        system_group = QGroupBox("System Information")
        system_layout = QVBoxLayout(system_group)

        try:
            import torch
            device_info = f"Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}"
            memory_info = f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory // 1024**2} MB" if torch.cuda.is_available() else "GPU Memory: N/A"
        except:
            device_info = "Device: Unknown"
            memory_info = "GPU Memory: Unknown"

        system_layout.addWidget(QLabel(device_info))
        system_layout.addWidget(QLabel(memory_info))

        layout.addWidget(system_group)

        # Connect GPU memory slider to label
        self.gpu_memory_slider.valueChanged.connect(
            lambda v: self.gpu_memory_label.setText(f"{v}%")
        )

    def create_metric_card(self, title, value, icon):
        """Create a metric display card"""
        card = QGroupBox()
        card.setMaximumHeight(80)
        layout = QVBoxLayout(card)
        layout.setContentsMargins(10, 5, 10, 5)

        # Title with icon
        title_layout = QHBoxLayout()
        title_layout.addWidget(QLabel(icon))
        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; color: #666;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # Value
        value_label = QLabel(value)
        value_label.setObjectName("value")
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)

        return card

    def update_metrics(self):
        """Update performance metrics"""
        import random

        # Simulate metrics (in real implementation, get from actual sources)
        fps = random.uniform(25, 60)
        gpu_usage = random.uniform(30, 90)
        memory_usage = random.uniform(1000, 8000)
        inference_time = random.uniform(10, 50)

        # Update labels
        self.fps_value_label.setText(f"{fps:.1f}")
        self.gpu_value_label.setText(f"{gpu_usage:.1f}%")
        self.memory_value_label.setText(f"{memory_usage:.0f} MB")
        self.inference_value_label.setText(f"{inference_time:.1f}ms")

        # Store history
        self.metrics_history['fps'].append(fps)
        self.metrics_history['gpu_usage'].append(gpu_usage)
        self.metrics_history['memory_usage'].append(memory_usage)
        self.metrics_history['inference_time'].append(inference_time)

        # Keep only last 60 values (1 minute of history)
        for key in self.metrics_history:
            if len(self.metrics_history[key]) > 60:
                self.metrics_history[key] = self.metrics_history[key][-60:]

        self.update_graph()

    def update_graph(self):
        """Update the performance graph"""
        metric = self.graph_metric.currentText().lower().replace(" ", "_")
        if metric in self.metrics_history:
            history = self.metrics_history[metric]
            if history:
                avg = sum(history) / len(history)
                min_val = min(history)
                max_val = max(history)

                graph_text = f"{self.graph_metric.currentText()} History\n"
                graph_text += f"Current: {history[-1]:.1f}\n"
                graph_text += f"Average: {avg:.1f}\n"
                graph_text += f"Min: {min_val:.1f} | Max: {max_val:.1f}\n"
                graph_text += f"Samples: {len(history)}"

                self.graph_widget.setText(graph_text)

    def clear_history(self):
        """Clear performance history"""
        for key in self.metrics_history:
            self.metrics_history[key] = []
        self.update_graph()

    def on_performance_mode_changed(self, mode):
        """Handle performance mode change"""
        if mode == "Quality":
            # Optimize for quality
            config.rocm.memory_fraction = 0.9
            self.gpu_memory_slider.setValue(90)
        elif mode == "Speed":
            # Optimize for speed
            config.rocm.memory_fraction = 0.7
            self.gpu_memory_slider.setValue(70)
        elif mode == "Balanced":
            # Balanced settings
            config.rocm.memory_fraction = 0.8
            self.gpu_memory_slider.setValue(80)
        # Custom mode allows manual adjustment

        config.save_config()

    def on_gpu_memory_changed(self, value):
        """Handle GPU memory limit change"""
        config.rocm.memory_fraction = value / 100.0
        config.save_config()

    def on_thread_count_changed(self, count):
        """Handle thread count change"""
        # In real implementation, this would update the thread pool
        pass

    def update_frame_metrics(self, data):
        """Update metrics from frame data"""
        # In real implementation, extract metrics from frame processing data
        pass

    def get_current_metrics(self):
        """Get current performance metrics"""
        if self.metrics_history['fps']:
            return {
                "fps": self.metrics_history['fps'][-1],
                "gpu_usage": self.metrics_history['gpu_usage'][-1],
                "memory_usage": self.metrics_history['memory_usage'][-1]
            }
        return {"fps": 0, "gpu_usage": 0, "memory_usage": 0}

class SettingsDialog(QWidget):
    """Comprehensive settings dialog with organized tabs"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Settings - AI Video Player")
        self.setWindowModality(Qt.WindowModality.ApplicationModal)
        self.resize(600, 500)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)

        # Settings tabs
        self.settings_tabs = QTabWidget()

        # General settings
        self.setup_general_tab()

        # Video settings
        self.setup_video_tab()

        # AI/GPU settings
        self.setup_ai_gpu_tab()

        # Interface settings
        self.setup_interface_tab()

        # Advanced settings
        self.setup_advanced_tab()

        layout.addWidget(self.settings_tabs)

        # Buttons
        button_layout = QHBoxLayout()

        self.reset_btn = QPushButton("Reset to Defaults")
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_btn)

        button_layout.addStretch()

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.close)
        button_layout.addWidget(self.cancel_btn)

        self.apply_btn = QPushButton("Apply")
        self.apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(self.apply_btn)

        self.ok_btn = QPushButton("OK")
        self.ok_btn.clicked.connect(self.accept_settings)
        button_layout.addWidget(self.ok_btn)

        layout.addLayout(button_layout)

    def setup_general_tab(self):
        """Setup general settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Startup settings
        startup_group = QGroupBox("Startup")
        startup_layout = QVBoxLayout(startup_group)

        self.auto_load_last_video = QCheckBox("Auto-load last video on startup")
        self.auto_load_last_video.setChecked(True)
        startup_layout.addWidget(self.auto_load_last_video)

        self.auto_load_last_model = QCheckBox("Auto-load last AI model on startup")
        self.auto_load_last_model.setChecked(True)
        startup_layout.addWidget(self.auto_load_last_model)

        self.check_updates = QCheckBox("Check for updates on startup")
        self.check_updates.setChecked(True)
        startup_layout.addWidget(self.check_updates)

        layout.addWidget(startup_group)

        # File associations
        files_group = QGroupBox("File Handling")
        files_layout = QVBoxLayout(files_group)

        self.remember_recent = QCheckBox("Remember recent files")
        self.remember_recent.setChecked(True)
        files_layout.addWidget(self.remember_recent)

        recent_layout = QHBoxLayout()
        recent_layout.addWidget(QLabel("Recent files limit:"))
        self.recent_limit = QSpinBox()
        self.recent_limit.setRange(5, 50)
        self.recent_limit.setValue(10)
        recent_layout.addWidget(self.recent_limit)
        recent_layout.addStretch()
        files_layout.addLayout(recent_layout)

        layout.addWidget(files_group)

        layout.addStretch()
        self.settings_tabs.addTab(tab, "General")

    def setup_video_tab(self):
        """Setup video settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Playback settings
        playback_group = QGroupBox("Playback")
        playback_layout = QVBoxLayout(playback_group)

        # Default quality
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("Default Quality:"))
        self.default_quality = QComboBox()
        self.default_quality.addItems(["Low", "Medium", "High", "Ultra"])
        self.default_quality.setCurrentText("High")
        quality_layout.addWidget(self.default_quality)
        quality_layout.addStretch()
        playback_layout.addLayout(quality_layout)

        # Hardware acceleration
        self.hardware_decode = QCheckBox("Hardware decoding")
        self.hardware_decode.setChecked(True)
        playback_layout.addWidget(self.hardware_decode)

        self.hardware_encode = QCheckBox("Hardware encoding")
        self.hardware_encode.setChecked(True)
        playback_layout.addWidget(self.hardware_encode)

        layout.addWidget(playback_group)

        # Output settings
        output_group = QGroupBox("Output")
        output_layout = QVBoxLayout(output_group)

        # Output format
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("Default Output Format:"))
        self.output_format = QComboBox()
        self.output_format.addItems(["MP4", "AVI", "MKV", "MOV", "WebM"])
        self.output_format.setCurrentText("MP4")
        format_layout.addWidget(self.output_format)
        format_layout.addStretch()
        output_layout.addLayout(format_layout)

        # Output directory
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("Output Directory:"))
        self.output_dir = QLabel("~/Videos/AI_Enhanced")
        self.output_dir.setStyleSheet("border: 1px solid gray; padding: 5px;")
        dir_layout.addWidget(self.output_dir)

        self.browse_output_btn = QPushButton("Browse...")
        self.browse_output_btn.clicked.connect(self.browse_output_directory)
        dir_layout.addWidget(self.browse_output_btn)
        output_layout.addLayout(dir_layout)

        layout.addWidget(output_group)

        layout.addStretch()
        self.settings_tabs.addTab(tab, "Video")

    def setup_ai_gpu_tab(self):
        """Setup AI/GPU settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # GPU settings
        gpu_group = QGroupBox("GPU Settings")
        gpu_layout = QVBoxLayout(gpu_group)

        # Device selection
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("Compute Device:"))
        self.compute_device = QComboBox()
        self.compute_device.addItems(["Auto", "GPU (ROCm)", "CPU"])
        self.compute_device.setCurrentText("Auto")
        device_layout.addWidget(self.compute_device)
        device_layout.addStretch()
        gpu_layout.addLayout(device_layout)

        # Memory settings
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(QLabel("GPU Memory Fraction:"))
        self.gpu_memory_fraction = QSlider(Qt.Orientation.Horizontal)
        self.gpu_memory_fraction.setRange(10, 100)
        self.gpu_memory_fraction.setValue(80)
        self.gpu_memory_fraction_label = QLabel("80%")
        memory_layout.addWidget(self.gpu_memory_fraction)
        memory_layout.addWidget(self.gpu_memory_fraction_label)
        gpu_layout.addLayout(memory_layout)

        # Mixed precision
        self.mixed_precision = QCheckBox("Use mixed precision (FP16)")
        self.mixed_precision.setChecked(True)
        self.mixed_precision.setToolTip("Faster processing with slightly reduced precision")
        gpu_layout.addWidget(self.mixed_precision)

        layout.addWidget(gpu_group)

        # AI Model settings
        ai_group = QGroupBox("AI Model Settings")
        ai_layout = QVBoxLayout(ai_group)

        # Default model
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Default Model:"))
        self.default_model = QComboBox()
        self.default_model.addItems(["None", "Real-ESRGAN", "Waifu2x", "EDSR"])
        model_layout.addWidget(self.default_model)
        model_layout.addStretch()
        ai_layout.addLayout(model_layout)

        # Auto-download
        self.auto_download_models = QCheckBox("Auto-download missing models")
        self.auto_download_models.setChecked(False)
        ai_layout.addWidget(self.auto_download_models)

        # Model cache
        cache_layout = QHBoxLayout()
        cache_layout.addWidget(QLabel("Model Cache Size:"))
        self.model_cache_size = QSpinBox()
        self.model_cache_size.setRange(1, 10)
        self.model_cache_size.setValue(3)
        self.model_cache_size.setSuffix(" models")
        cache_layout.addWidget(self.model_cache_size)
        cache_layout.addStretch()
        ai_layout.addLayout(cache_layout)

        layout.addWidget(ai_group)

        # Connect GPU memory slider
        self.gpu_memory_fraction.valueChanged.connect(
            lambda v: self.gpu_memory_fraction_label.setText(f"{v}%")
        )

        layout.addStretch()
        self.settings_tabs.addTab(tab, "AI/GPU")

    def setup_interface_tab(self):
        """Setup interface settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Appearance
        appearance_group = QGroupBox("Appearance")
        appearance_layout = QVBoxLayout(appearance_group)

        # Theme
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("Theme:"))
        self.theme = QComboBox()
        self.theme.addItems(["Dark", "Light", "Auto"])
        self.theme.setCurrentText("Dark")
        theme_layout.addWidget(self.theme)
        theme_layout.addStretch()
        appearance_layout.addLayout(theme_layout)

        # UI scale
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("UI Scale:"))
        self.ui_scale = QSlider(Qt.Orientation.Horizontal)
        self.ui_scale.setRange(80, 150)
        self.ui_scale.setValue(100)
        self.ui_scale_label = QLabel("100%")
        scale_layout.addWidget(self.ui_scale)
        scale_layout.addWidget(self.ui_scale_label)
        appearance_layout.addLayout(scale_layout)

        layout.addWidget(appearance_group)

        # Window settings
        window_group = QGroupBox("Window")
        window_layout = QVBoxLayout(window_group)

        self.remember_window_size = QCheckBox("Remember window size and position")
        self.remember_window_size.setChecked(True)
        window_layout.addWidget(self.remember_window_size)

        self.start_fullscreen = QCheckBox("Start in fullscreen mode")
        self.start_fullscreen.setChecked(False)
        window_layout.addWidget(self.start_fullscreen)

        self.show_performance_overlay = QCheckBox("Show performance overlay")
        self.show_performance_overlay.setChecked(True)
        window_layout.addWidget(self.show_performance_overlay)

        layout.addWidget(window_group)

        # Connect UI scale slider
        self.ui_scale.valueChanged.connect(
            lambda v: self.ui_scale_label.setText(f"{v}%")
        )

        layout.addStretch()
        self.settings_tabs.addTab(tab, "Interface")

    def setup_advanced_tab(self):
        """Setup advanced settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Performance
        perf_group = QGroupBox("Performance")
        perf_layout = QVBoxLayout(perf_group)

        # Thread count
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Worker Threads:"))
        self.worker_threads = QSpinBox()
        self.worker_threads.setRange(1, 32)
        self.worker_threads.setValue(4)
        thread_layout.addWidget(self.worker_threads)
        thread_layout.addStretch()
        perf_layout.addLayout(thread_layout)

        # Buffer size
        buffer_layout = QHBoxLayout()
        buffer_layout.addWidget(QLabel("Frame Buffer Size:"))
        self.frame_buffer_size = QSpinBox()
        self.frame_buffer_size.setRange(1, 100)
        self.frame_buffer_size.setValue(10)
        self.frame_buffer_size.setSuffix(" frames")
        buffer_layout.addWidget(self.frame_buffer_size)
        buffer_layout.addStretch()
        perf_layout.addLayout(buffer_layout)

        layout.addWidget(perf_group)

        # Logging
        logging_group = QGroupBox("Logging")
        logging_layout = QVBoxLayout(logging_group)

        # Log level
        log_layout = QHBoxLayout()
        log_layout.addWidget(QLabel("Log Level:"))
        self.log_level = QComboBox()
        self.log_level.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level.setCurrentText("INFO")
        log_layout.addWidget(self.log_level)
        log_layout.addStretch()
        logging_layout.addLayout(log_layout)

        self.enable_file_logging = QCheckBox("Enable file logging")
        self.enable_file_logging.setChecked(True)
        logging_layout.addWidget(self.enable_file_logging)

        self.enable_performance_logging = QCheckBox("Enable performance logging")
        self.enable_performance_logging.setChecked(False)
        logging_layout.addWidget(self.enable_performance_logging)

        layout.addWidget(logging_group)

        # Experimental
        experimental_group = QGroupBox("Experimental Features")
        experimental_layout = QVBoxLayout(experimental_group)

        self.enable_experimental = QCheckBox("Enable experimental features")
        self.enable_experimental.setChecked(False)
        experimental_layout.addWidget(self.enable_experimental)

        self.enable_model_optimization = QCheckBox("Enable model optimization")
        self.enable_model_optimization.setChecked(True)
        experimental_layout.addWidget(self.enable_model_optimization)

        layout.addWidget(experimental_group)

        layout.addStretch()
        self.settings_tabs.addTab(tab, "Advanced")

    def browse_output_directory(self):
        """Browse for output directory"""
        directory = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if directory:
            self.output_dir.setText(directory)

    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        reply = QMessageBox.question(
            self, "Reset Settings",
            "Are you sure you want to reset all settings to defaults?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Reset all controls to default values
            self.auto_load_last_video.setChecked(True)
            self.auto_load_last_model.setChecked(True)
            self.check_updates.setChecked(True)
            self.default_quality.setCurrentText("High")
            self.theme.setCurrentText("Dark")
            self.gpu_memory_fraction.setValue(80)
            # ... reset other controls

    def apply_settings(self):
        """Apply settings without closing dialog"""
        self.save_settings()

    def accept_settings(self):
        """Apply settings and close dialog"""
        self.save_settings()
        self.close()

    def save_settings(self):
        """Save all settings to configuration"""
        # Update config object
        config.ui.theme = self.theme.currentText().lower()
        config.ui.fullscreen = self.start_fullscreen.isChecked()
        config.ui.show_performance_overlay = self.show_performance_overlay.isChecked()
        config.ui.auto_save_settings = True

        config.rocm.memory_fraction = self.gpu_memory_fraction.value() / 100.0
        config.rocm.use_mixed_precision = self.mixed_precision.isChecked()

        config.video.quality_preset = self.default_quality.currentText().lower()
        config.video.output_format = self.output_format.currentText().lower()
        config.video.hardware_decode = self.hardware_decode.isChecked()
        config.video.hardware_encode = self.hardware_encode.isChecked()

        # Save to file
        config.save_config()

        QMessageBox.information(self, "Settings", "Settings saved successfully!")

    def exec(self):
        """Show the dialog"""
        self.show()
        self.raise_()
        self.activateWindow()

class MainWindow(QMainWindow):
    """Main application window with comprehensive AI video enhancement features"""

    def __init__(self):
        super().__init__()
        self.settings = QSettings()
        self.performance_logger = PerformanceLogger("ui")

        # Core components
        self.video_widget = None
        self.controls_panel = None
        self.model_panel = None
        self.performance_panel = None
        self.settings_dialog = None

        # State
        self.current_video_path = None
        self.is_playing = False
        self.current_model = None
        self.upscaling_enabled = False

        # Timers
        self.status_timer = QTimer()
        self.performance_timer = QTimer()

        self.init_ui()
        self.setup_connections()
        self.restore_settings()

        logger.info("Main window initialized")

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("AI-Enhanced Video Player - All Models Supported")
        self.setMinimumSize(1024, 768)

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Create splitter for main content
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)

        # Left panel for video and controls
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # Video widget
        self.video_widget = VideoWidget()
        left_layout.addWidget(self.video_widget, 1)

        # Controls panel
        self.controls_panel = ControlsPanel()
        left_layout.addWidget(self.controls_panel)

        main_splitter.addWidget(left_widget)

        # Right panel for AI models and settings
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Create tabs for different panels
        tab_widget = QTabWidget()

        # Model selection and configuration tab
        self.model_panel = ModelPanel()
        tab_widget.addTab(self.model_panel, "AI Models")

        # Performance monitoring tab
        self.performance_panel = PerformancePanel()
        tab_widget.addTab(self.performance_panel, "Performance")

        # Settings tab (placeholder for now)
        settings_widget = QWidget()
        tab_widget.addTab(settings_widget, "Settings")

        right_layout.addWidget(tab_widget)
        main_splitter.addWidget(right_widget)

        # Set splitter proportions (70% video, 30% controls)
        main_splitter.setSizes([700, 300])

        # Create menu bar
        self.create_menu_bar()

        # Create toolbar
        self.create_toolbar()

        # Create quick access toolbar
        self.create_quick_access_toolbar()

        # Create status bar
        self.create_status_bar()

        # Create dock widgets
        self.create_dock_widgets()

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        open_action = QAction("&Open Video...", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.setStatusTip("Open a video file")
        open_action.triggered.connect(self.open_video)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip("Exit the application")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # AI menu
        ai_menu = menubar.addMenu("&AI Models")

        load_model_action = QAction("&Load Model...", self)
        load_model_action.setStatusTip("Load an AI upscaling model")
        load_model_action.triggered.connect(self.load_ai_model)
        ai_menu.addAction(load_model_action)

        unload_models_action = QAction("&Unload All Models", self)
        unload_models_action.setStatusTip("Unload all loaded AI models")
        unload_models_action.triggered.connect(self.unload_all_models)
        ai_menu.addAction(unload_models_action)

        ai_menu.addSeparator()

        # Add submenu for each model category
        traditional_menu = ai_menu.addMenu("Traditional SR")
        esrgan_menu = ai_menu.addMenu("ESRGAN Family")
        waifu2x_menu = ai_menu.addMenu("Waifu2x Variants")
        transformer_menu = ai_menu.addMenu("Transformer Models")
        video_menu = ai_menu.addMenu("Video Models")
        realtime_menu = ai_menu.addMenu("Real-time Models")

        # Populate model menus
        self.populate_model_menus({
            "Traditional SR": traditional_menu,
            "ESRGAN Family": esrgan_menu,
            "Waifu2x Variants": waifu2x_menu,
            "Transformer Models": transformer_menu,
            "Video Models": video_menu,
            "Real-time Models": realtime_menu
        })

        # View menu
        view_menu = menubar.addMenu("&View")

        fullscreen_action = QAction("&Fullscreen", self)
        fullscreen_action.setShortcut(QKeySequence.StandardKey.FullScreen)
        fullscreen_action.setCheckable(True)
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        settings_action = QAction("&Settings...", self)
        settings_action.setShortcut(QKeySequence.StandardKey.Preferences)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def populate_model_menus(self, menus: dict):
        """Populate model category menus"""
        model_categories = {
            "Traditional SR": ["srcnn", "vdsr", "edsr", "mdsr"],
            "ESRGAN Family": ["esrgan", "real_esrgan", "real_esrgan_x4", "real_esrgan_anime"],
            "Waifu2x Variants": ["waifu2x", "waifu2x_cunet", "waifu2x_upconv", "waifu2x_anime_style"],
            "Transformer Models": ["swinir", "hat", "swin2sr"],
            "Video Models": ["basicvsr", "basicvsr_plus", "iconvsr", "tdan", "edvr"],
            "Real-time Models": ["realtime_esrgan", "fast_srgan", "efficient_sr"]
        }

        for category, models in model_categories.items():
            if category in menus:
                menu = menus[category]
                for model_name in models:
                    action = QAction(model_name.replace("_", " ").title(), self)
                    action.setData(model_name)
                    action.triggered.connect(lambda checked, m=model_name: self.load_specific_model(m))
                    menu.addAction(action)

    def create_toolbar(self):
        """Create application toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)

        # Open video
        open_action = QAction("Open", self)
        open_action.setStatusTip("Open video file")
        open_action.triggered.connect(self.open_video)
        toolbar.addAction(open_action)

        toolbar.addSeparator()

        # Play/Pause
        self.play_action = QAction("Play", self)
        self.play_action.setStatusTip("Play/Pause video")
        self.play_action.triggered.connect(self.toggle_playback)
        toolbar.addAction(self.play_action)

        # Stop
        stop_action = QAction("Stop", self)
        stop_action.setStatusTip("Stop video")
        stop_action.triggered.connect(self.stop_video)
        toolbar.addAction(stop_action)

        toolbar.addSeparator()

        # AI Toggle
        self.ai_toggle_action = QAction("AI Off", self)
        self.ai_toggle_action.setCheckable(True)
        self.ai_toggle_action.setStatusTip("Toggle AI upscaling")
        self.ai_toggle_action.triggered.connect(self.toggle_ai_upscaling)
        toolbar.addAction(self.ai_toggle_action)

        # Model selector
        toolbar.addWidget(QLabel("Model:"))
        self.model_selector = QComboBox()
        self.model_selector.setMinimumWidth(150)
        self.model_selector.currentTextChanged.connect(self.on_model_changed)
        toolbar.addWidget(self.model_selector)

        # Update model list
        self.update_model_selector()

    def create_quick_access_toolbar(self):
        """Create quick access toolbar with essential controls"""
        quick_toolbar = self.addToolBar("Quick Access")
        quick_toolbar.setObjectName("QuickAccessToolbar")
        quick_toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonIconOnly)

        # Quick download section
        quick_toolbar.addSeparator()
        quick_toolbar.addWidget(QLabel("Quick Download: "))

        # Essential models button
        essential_btn = QPushButton("📥 Essential")
        essential_btn.setToolTip("Download Real-ESRGAN models (recommended)")
        essential_btn.clicked.connect(lambda: download_manager.download_essential_models())
        essential_btn.setMaximumHeight(30)
        quick_toolbar.addWidget(essential_btn)

        # Anime models button
        anime_btn = QPushButton("🎌 Anime")
        anime_btn.setToolTip("Download anime-optimized models")
        anime_btn.clicked.connect(lambda: download_manager.download_anime_models())
        anime_btn.setMaximumHeight(30)
        quick_toolbar.addWidget(anime_btn)

        quick_toolbar.addSeparator()

        # Quick settings
        quick_toolbar.addWidget(QLabel("Quick Settings: "))

        # Performance mode
        perf_combo = QComboBox()
        perf_combo.addItems(["Balanced", "Quality", "Speed"])
        perf_combo.setCurrentText("Balanced")
        perf_combo.setToolTip("Performance mode")
        perf_combo.currentTextChanged.connect(self.on_quick_performance_mode_changed)
        perf_combo.setMaximumHeight(30)
        quick_toolbar.addWidget(perf_combo)

        # GPU memory slider
        quick_toolbar.addWidget(QLabel("GPU Mem:"))
        gpu_slider = QSlider(Qt.Orientation.Horizontal)
        gpu_slider.setRange(50, 100)
        gpu_slider.setValue(80)
        gpu_slider.setMaximumWidth(100)
        gpu_slider.setMaximumHeight(30)
        gpu_slider.setToolTip("GPU Memory Usage")
        gpu_slider.valueChanged.connect(self.on_quick_gpu_memory_changed)
        quick_toolbar.addWidget(gpu_slider)

        self.gpu_mem_label = QLabel("80%")
        self.gpu_mem_label.setMinimumWidth(30)
        quick_toolbar.addWidget(self.gpu_mem_label)

        # Connect slider to label
        gpu_slider.valueChanged.connect(lambda v: self.gpu_mem_label.setText(f"{v}%"))

        quick_toolbar.addSeparator()

        # Quick model actions
        quick_toolbar.addWidget(QLabel("Models: "))

        # Load model button
        load_model_btn = QPushButton("📂 Load")
        load_model_btn.setToolTip("Quick load model")
        load_model_btn.clicked.connect(self.show_quick_model_selector)
        load_model_btn.setMaximumHeight(30)
        quick_toolbar.addWidget(load_model_btn)

        # Unload all button
        unload_btn = QPushButton("🗑️ Unload All")
        unload_btn.setToolTip("Unload all models")
        unload_btn.clicked.connect(self.unload_all_models)
        unload_btn.setMaximumHeight(30)
        quick_toolbar.addWidget(unload_btn)

    def on_quick_performance_mode_changed(self, mode):
        """Handle quick performance mode change"""
        if mode == "Quality":
            config.rocm.memory_fraction = 0.9
        elif mode == "Speed":
            config.rocm.memory_fraction = 0.7
        else:  # Balanced
            config.rocm.memory_fraction = 0.8
        config.save_config()

    def on_quick_gpu_memory_changed(self, value):
        """Handle quick GPU memory change"""
        config.rocm.memory_fraction = value / 100.0
        config.save_config()

    def show_quick_model_selector(self):
        """Show quick model selector dialog"""
        from PyQt6.QtWidgets import QInputDialog

        available_models = model_manager.get_available_models()
        if not available_models:
            QMessageBox.information(self, "No Models", "No models available. Please download models first.")
            return

        model, ok = QInputDialog.getItem(
            self, "Select Model", "Choose a model to load:",
            [m.replace("_", " ").title() for m in available_models],
            0, False
        )

        if ok and model:
            # Convert back to model name
            model_name = model.lower().replace(" ", "_")
            if self.model_panel:
                self.model_panel.load_model(model_name)

    def create_status_bar(self):
        """Create status bar with performance indicators"""
        status_bar = self.statusBar()

        # Main status label
        self.status_label = QLabel("Ready")
        status_bar.addWidget(self.status_label)

        # Performance indicators
        status_bar.addPermanentWidget(QLabel("FPS:"))
        self.fps_label = QLabel("0")
        status_bar.addPermanentWidget(self.fps_label)

        status_bar.addPermanentWidget(QLabel("GPU:"))
        self.gpu_label = QLabel("0%")
        status_bar.addPermanentWidget(self.gpu_label)

        status_bar.addPermanentWidget(QLabel("Memory:"))
        self.memory_label = QLabel("0 MB")
        status_bar.addPermanentWidget(self.memory_label)

        # Progress bar for operations
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_bar.addPermanentWidget(self.progress_bar)

    def create_dock_widgets(self):
        """Create dockable widgets"""
        # Model information dock
        model_info_dock = QDockWidget("Model Information", self)
        model_info_widget = QTextEdit()
        model_info_widget.setMaximumHeight(150)
        model_info_widget.setReadOnly(True)
        model_info_dock.setWidget(model_info_widget)
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, model_info_dock)

        # Log dock
        log_dock = QDockWidget("Logs", self)
        self.log_widget = QTextEdit()
        self.log_widget.setMaximumHeight(150)
        self.log_widget.setReadOnly(True)
        log_dock.setWidget(self.log_widget)
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, log_dock)

        # Tabify docks
        self.tabifyDockWidget(model_info_dock, log_dock)

    def setup_connections(self):
        """Setup signal connections"""
        # Video widget connections
        if self.video_widget:
            self.video_widget.frame_ready.connect(self.on_frame_ready)
            self.video_widget.playback_finished.connect(self.on_playback_finished)

        # Controls panel connections
        if self.controls_panel:
            self.controls_panel.play_pause_clicked.connect(self.toggle_playback)
            self.controls_panel.stop_clicked.connect(self.stop_video)
            self.controls_panel.position_changed.connect(self.seek_video)

        # Model panel connections
        if self.model_panel:
            self.model_panel.model_selected.connect(self.on_model_selected)
            self.model_panel.model_loaded.connect(self.on_model_loaded)
            self.model_panel.model_unloaded.connect(self.on_model_unloaded)

        # Timers
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second

        self.performance_timer.timeout.connect(self.update_performance)
        self.performance_timer.start(500)  # Update every 500ms

    def update_model_selector(self):
        """Update the model selector combo box"""
        self.model_selector.clear()
        self.model_selector.addItem("None")

        available_models = model_manager.get_available_models()
        for model_name in available_models:
            display_name = model_name.replace("_", " ").title()
            self.model_selector.addItem(display_name, model_name)

    def open_video(self):
        """Open video file dialog"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Video File",
            "",
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v *.3gp);;All Files (*)"
        )

        if file_path:
            self.load_video(file_path)

    def load_video(self, file_path: str):
        """Load a video file"""
        try:
            self.current_video_path = file_path

            if self.video_widget:
                self.video_widget.load_video(file_path)

            self.status_label.setText(f"Loaded: {Path(file_path).name}")
            logger.info(f"Loaded video: {file_path}")

        except Exception as e:
            logger.error(f"Error loading video: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load video:\n{e}")

    def toggle_playback(self):
        """Toggle video playback"""
        if self.video_widget and self.current_video_path:
            if self.is_playing:
                self.video_widget.pause()
                self.play_action.setText("Play")
                self.is_playing = False
            else:
                self.video_widget.play()
                self.play_action.setText("Pause")
                self.is_playing = True

    def stop_video(self):
        """Stop video playback"""
        if self.video_widget:
            self.video_widget.stop()
            self.play_action.setText("Play")
            self.is_playing = False

    def seek_video(self, position: float):
        """Seek to position in video (0.0 to 1.0)"""
        if self.video_widget:
            self.video_widget.seek(position)

    def toggle_ai_upscaling(self):
        """Toggle AI upscaling on/off"""
        self.upscaling_enabled = self.ai_toggle_action.isChecked()

        if self.upscaling_enabled:
            self.ai_toggle_action.setText("AI On")
            if self.video_widget:
                self.video_widget.enable_ai_upscaling(True)
        else:
            self.ai_toggle_action.setText("AI Off")
            if self.video_widget:
                self.video_widget.enable_ai_upscaling(False)

        logger.info(f"AI upscaling {'enabled' if self.upscaling_enabled else 'disabled'}")

    def load_ai_model(self):
        """Show model loading dialog"""
        if self.model_panel:
            self.model_panel.show_load_dialog()

    def load_specific_model(self, model_name: str):
        """Load a specific AI model"""
        if self.model_panel:
            self.model_panel.load_model(model_name)

    def unload_all_models(self):
        """Unload all AI models"""
        model_manager.unload_all_models()
        self.update_model_selector()
        self.status_label.setText("All models unloaded")
        logger.info("All AI models unloaded")

    def on_model_changed(self, model_name: str):
        """Handle model selection change"""
        if model_name == "None":
            self.current_model = None
        else:
            # Get actual model name from combo box data
            index = self.model_selector.currentIndex()
            if index > 0:  # Skip "None" option
                self.current_model = self.model_selector.itemData(index)

        if self.video_widget:
            self.video_widget.set_ai_model(self.current_model)

    def on_model_selected(self, model_name: str):
        """Handle model selection from model panel"""
        # Update combo box
        for i in range(self.model_selector.count()):
            if self.model_selector.itemData(i) == model_name:
                self.model_selector.setCurrentIndex(i)
                break

    def on_model_loaded(self, model_name: str):
        """Handle model loaded event"""
        self.update_model_selector()
        self.status_label.setText(f"Loaded model: {model_name}")

    def on_model_unloaded(self, model_name: str):
        """Handle model unloaded event"""
        self.update_model_selector()
        self.status_label.setText(f"Unloaded model: {model_name}")

    def on_frame_ready(self, frame_data):
        """Handle new frame from video widget"""
        # Update performance metrics
        if self.performance_panel:
            self.performance_panel.update_frame_metrics(frame_data)

    def on_playback_finished(self):
        """Handle playback finished"""
        self.is_playing = False
        self.play_action.setText("Play")
        self.status_label.setText("Playback finished")

    def update_status(self):
        """Update status bar information"""
        if self.video_widget:
            # Update playback info
            pass

    def update_performance(self):
        """Update performance indicators"""
        if self.performance_panel:
            metrics = self.performance_panel.get_current_metrics()

            self.fps_label.setText(f"{metrics.get('fps', 0):.1f}")
            self.gpu_label.setText(f"{metrics.get('gpu_usage', 0):.1f}%")
            self.memory_label.setText(f"{metrics.get('memory_usage', 0):.0f} MB")

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def show_settings(self):
        """Show settings dialog"""
        if not self.settings_dialog:
            self.settings_dialog = SettingsDialog(self)

        self.settings_dialog.exec()

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About AI Video Player",
            "AI-Enhanced Video Player\n\n"
            "Supports ALL available AI upscaling models:\n"
            "• Traditional SR (SRCNN, VDSR, EDSR, MDSR)\n"
            "• ESRGAN Family (ESRGAN, Real-ESRGAN variants)\n"
            "• Waifu2x Variants (CUNet, UpConv, Anime)\n"
            "• Transformer Models (SwinIR, HAT, Swin2SR)\n"
            "• Video Models (BasicVSR, IconVSR, TDAN, EDVR)\n"
            "• Real-time Models (Fast-SRGAN, Efficient-SR)\n"
            "• Diffusion Models (LDSR, Stable Diffusion)\n\n"
            "With ROCm acceleration for AMD GPUs"
        )

    def save_settings(self):
        """Save window settings"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
        self.settings.setValue("current_model", self.current_model)
        self.settings.setValue("ai_enabled", self.upscaling_enabled)

    def restore_settings(self):
        """Restore window settings"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        window_state = self.settings.value("windowState")
        if window_state:
            self.restoreState(window_state)

        # Restore model selection
        saved_model = self.settings.value("current_model")
        if saved_model:
            for i in range(self.model_selector.count()):
                if self.model_selector.itemData(i) == saved_model:
                    self.model_selector.setCurrentIndex(i)
                    break

        # Restore AI state
        ai_enabled = self.settings.value("ai_enabled", False, type=bool)
        self.ai_toggle_action.setChecked(ai_enabled)
        self.toggle_ai_upscaling()

    def closeEvent(self, event):
        """Handle window close event"""
        self.save_settings()

        # Cleanup
        if self.video_widget:
            self.video_widget.cleanup()

        model_manager.unload_all_models()

        event.accept()
